import os
from fpdf import FPDF

class PDF(FPDF):
    def header(self):
        self.set_font('DejaVu', 'B', 12)
        self.cell(0, 10, '<PERSON> - CV Développeur Python', 0, 1, 'C')
        self.ln(10)

    def footer(self):
        self.set_y(-15)
        self.set_font('DejaVu', 'I', 8)
        self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')

    def chapter_title(self, title):
        self.set_font('DejaVu', 'B', 12)
        self.cell(0, 10, title, 0, 1, 'L')
        self.ln(4)

    def chapter_body(self, body):
        self.set_font('DejaVu', '', 12)
        self.multi_cell(0, 10, body)
        self.ln()

# Obtenir le chemin absolu du répertoire des polices
base_dir = os.path.dirname(os.path.abspath(__file__))
fonts_dir = os.path.join(base_dir, 'dejavu-fonts-ttf-2.37', 'ttf')

# Créer une instance de la classe PDF personnalisée
pdf = PDF()

# Ajouter une police Unicode personnalisée
pdf.add_font('DejaVu', '', os.path.join(fonts_dir, 'DejaVuSans.ttf'), uni=True)
pdf.add_font('DejaVu', 'B', os.path.join(fonts_dir, 'DejaVuSans-Bold.ttf'), uni=True)
pdf.add_font('DejaVu', 'I', os.path.join(fonts_dir, 'DejaVuSans-Oblique.ttf'), uni=True)

# Définir la police par défaut
pdf.set_font('DejaVu', '', 12)

# Ajouter une page
pdf.add_page()

# Informations d'en-tête
pdf.cell(0, 10, "Antoine Didier Bayanga", ln=True, align='C')
pdf.cell(0, 10, "Développeur Python | FastAPI, Django, Elasticsearch", ln=True, align='C')
pdf.cell(0, 10, "Yaoundé, Région du Centre, Cameroun", ln=True, align='C')
pdf.cell(0, 10, "Mobile: +237 691796631 | Email: <EMAIL>", ln=True, align='C')
pdf.cell(0, 10, "LinkedIn: linkedin.com/in/antoine-didier-bayanga-710630116", ln=True, align='C')
pdf.ln(10)

# Résumé
pdf.chapter_title("Résumé")
summary_text = (
    "Développeur Python orienté résultats, avec un bilan éprouvé dans la conception et la mise en œuvre "
    "d'applications web évolutives et de solutions axées sur les données. Maîtrise de Python, FastAPI, Django, "
    "Elasticsearch. Expérience dans la construction et l'amélioration de plateformes e-commerce, de moteurs de "
    "recherche personnalisés, de tableaux de bord et d'applications pilotées par les données. Apte à diriger "
    "des équipes pluridisciplinaires, à assurer la réutilisabilité et la maintenabilité du code, et à livrer "
    "des projets dans les délais. Solide expérience dans l'intégration de technologies diverses, l'optimisation "
    "des performances et la réduction des coûts."
)
pdf.chapter_body(summary_text)

# Expérience professionnelle
pdf.chapter_title("Expérience professionnelle")

# Toolbrothers Powertools GmbH
pdf.chapter_title("Toolbrothers Powertools GmbH")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "Développeur Python | Octobre 2022 - Présent | Göttingen, Allemagne", ln=True)
pdf.set_font("Arial", '', 12)
toolbrothers_text = (
    "- Développer un moteur de recherche personnalisé utilisant FastAPI et Elasticsearch pour remplacer Findologic, "
    "réduisant les coûts et améliorant la précision de la recherche.\n"
    "  - Frontend : Vanilla HTML/CSS/JS\n"
    "  - Backend : FastAPI et Elasticsearch\n"
    "  - Concevoir pour être intégré à n'importe quel site web.\n"
    "- Créer un tableau de bord d'administration des fournisseurs utilisant Python (FastAPI) et Vue.js pour offrir "
    "une expérience rationalisée.\n"
    "- Développer un tableau de bord client pour gérer les fonctionnalités de recherche avec FastAPI et Jinja2.\n"
    "- Mettre en œuvre des statistiques complètes sur les liens d'affiliation en utilisant Python, fournissant des "
    "informations précieuses pour les stratégies marketing.\n"
    "- Intégrer Pimcore pour les traductions multilingues :\n"
    "  - Utiliser Python pour le transfert et le traitement des données entre CS-Cart et Pimcore.\n"
    "  - Tirer parti de l'API GraphQL de Pimcore pour une intégration transparente.\n"
    "- Développer plusieurs applications utilisant Vue.js et Python (FastAPI/Jinja2) dans le cadre de la transition "
    "hors de CS-Cart.\n"
    "- Utiliser Python pour récupérer et traiter les données des places de marché, générant des flux de données "
    "pour les télécharger sur diverses places de marché.\n"
    "- Créer des interfaces utilisateur dynamiques avec Vue.js, améliorant l'expérience utilisateur."
)
pdf.chapter_body(toolbrothers_text)

# DATICCUL
pdf.chapter_title("DATICCUL")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "Développeur Web | Septembre 2021 - Octobre 2022 | Douala, Cameroun", ln=True)
pdf.set_font("Arial", '', 12)
daticcul_text = (
    "- Concevoir et développer des applications web évolutives pour une société de microfinance spécialisée dans les "
    "transactions d'argent.\n"
    "- Construire des tableaux de bord clients et des API en utilisant Symfony 5, Laravel et Django pour une "
    "gestion efficace des données et le traitement des transactions.\n"
    "- Assurer la réutilisabilité et la maintenabilité du code, optimisant le processus de développement et "
    "réduisant le temps consacré aux futurs projets.\n"
    "- Mettre en place des mécanismes d'authentification et d'autorisation sécurisés pour protéger les données "
    "financières sensibles."
)
pdf.chapter_body(daticcul_text)

# Emas
pdf.chapter_title("Emas")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "Enseignant d'informatique | Avril 2022 - Juillet 2022 | Yaoundé, Cameroun", ln=True)
pdf.set_font("Arial", '', 12)
emas_text = (
    "- Enseigner des modules sur les systèmes d'exploitation et la programmation événementielle en VB.NET.\n"
    "- Fournir une expérience pratique avec les systèmes Linux et VB.NET pour les applications interactives."
)
pdf.chapter_body(emas_text)

# CIS
pdf.chapter_title("CIS")
pdf.set_font("DejaVu", 'I', 12)
pdf.cell(0, 10, "Enseignant d'informatique | Février 2018 - Juin 2022", ln=True)
pdf.set_font("DejaVu", '', 12)
cis_text = (
    "- Enseigner l'administration Linux, les réseaux et protocoles, la VoIP, les algorithmes avancés et la programmation "
    "(Python, Java, C++).\n"
    "- Concevoir des plans de cours, animer des séances pratiques et évaluer les performances des étudiants.\n"
    "- Servir de superviseur académique, offrant du mentorat et du soutien."
)
pdf.chapter_body(cis_text)

# Particular Destiny Suites Hôtel
pdf.chapter_title("Particular Destiny Suites Hôtel")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "Responsable informatique | Avril 2021 - Septembre 2021 | Douala, Cameroun", ln=True)
pdf.set_font("Arial", '', 12)
particular_destiny_text = (
    "- Gérer les équipements informatiques et administrer le système ERP de l'entreprise.\n"
    "- Assurer le bon fonctionnement de l'infrastructure informatique."
)
pdf.chapter_body(particular_destiny_text)

# SIAPPHARMA
pdf.chapter_title("SIAPPHARMA")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "Responsable informatique | Juillet 2018 - Mars 2021 | Douala, Cameroun", ln=True)
pdf.set_font("Arial", '', 12)
siappharma_text = (
    "- Diriger des initiatives de développement de logiciels et gérer l'infrastructure informatique.\n"
    "- Administrer le système ERP de l'entreprise."
)
pdf.chapter_body(siappharma_text)

# Institut du Retour d'EXperience - IREX
pdf.chapter_title("Institut du Retour d'EXperience - IREX")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "Stagiaire en services réseau | Octobre 2017 - Janvier 2018 | Yaoundé, Cameroun", ln=True)
pdf.set_font("Arial", '', 12)
irex_text = (
    "- Déployer des services réseau et améliorer la sécurité du réseau.\n"
    "- Mettre en œuvre des services DNS, DHCP, VPN et des outils de collaboration comme Nextcloud et Zimbra."
)
pdf.chapter_body(irex_text)

# Éducation
pdf.chapter_title("Éducation")
education_text = "Université de Yaoundé 1\nLicence en Informatique | 2012 - 2016"
pdf.chapter_body(education_text)

# Certifications
pdf.chapter_title("Certifications")
certifications_text = (
    "- Algorithmes et structures de données JavaScript (FreeCodeCamp)\n"
    "- Conception Web Responsive (FreeCodeCamp)\n"
    "- Python (Niveau de base) (FreeCodeCamp)\n"
    "- Bibliothèques Front-End (FreeCodeCamp)\n"
    "- Calcul scientifique avec Python (FreeCodeCamp)"
)
pdf.chapter_body(certifications_text)

# Compétences
pdf.chapter_title("Compétences")
skills_text = (
    "- Langages de programmation : Python, JavaScript, PHP\n"
    "- Frameworks et bibliothèques Python : FastAPI, Django, SQLAlchemy, Jinja 2, Elasticsearch\n"
    "- Frameworks front-end : Vue.js, Symfony, Laravel\n"
    "- Bases de données : MySQL, MariaDB, PostgreSQL\n"
    "- Outils et technologies : CS-Cart, Pimcore, GraphQL API, Git\n"
    "- Environnement de développement : Linux, Visual Studio Code\n"
    "- Langues : Français (Niveau natif), Anglais (Courant)"
)
pdf.chapter_body(skills_text)

# Enregistrer le PDF
pdf_output_path = "Antoine_Didier_Bayanga_CV_Developpeur_Python.pdf"
pdf.output(pdf_output_path)

print(f"PDF enregistré à {pdf_output_path}")