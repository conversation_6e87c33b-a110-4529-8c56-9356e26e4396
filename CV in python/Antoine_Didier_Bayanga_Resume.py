import os
from fpdf import FPDF

class PDF(FPDF):
    def header(self):
        self.set_font('DejaVu', 'B', 12)
        self.cell(0, 10, '<PERSON> - <PERSON>sume', 0, 1, 'C')
        self.ln(10)

    def footer(self):
        self.set_y(-15)
        self.set_font('DejaVu', 'I', 8)
        self.cell(0, 10, f'Page {self.page_no()}', 0, 0, 'C')

    def chapter_title(self, title):
        self.set_font('DejaVu', 'B', 12)
        self.cell(0, 10, title, 0, 1, 'L')
        self.ln(4)

    def chapter_body(self, body):
        self.set_font('DejaVu', '', 12)
        self.multi_cell(0, 10, body)
        self.ln()

# Get the absolute path to the fonts directory
base_dir = os.path.dirname(os.path.abspath(__file__))
fonts_dir = os.path.join(base_dir, 'dejavu-fonts-ttf-2.37', 'ttf')

# Create an instance of the custom PDF class
pdf = PDF()

# Add a custom Unicode font
pdf.add_font('DejaVu', '', os.path.join(fonts_dir, 'DejaVuSans.ttf'), uni=True)
pdf.add_font('DejaVu', 'B', os.path.join(fonts_dir, 'DejaVuSans-Bold.ttf'), uni=True)
pdf.add_font('DejaVu', 'I', os.path.join(fonts_dir, 'DejaVuSans-Oblique.ttf'), uni=True)

# Set the default font
pdf.set_font('DejaVu', '', 12)

# Add a page
pdf.add_page()

# Set font and add information
pdf.set_font("Arial", size=12)
pdf.cell(0, 10, "Antoine Didier Bayanga", ln=True, align='C')
pdf.cell(0, 10, "Full-stack Developer | Python, FastAPI, JavaScript, VueJS, PHP", ln=True, align='C')
pdf.cell(0, 10, "Yaoundé, Région du Centre, Cameroun", ln=True, align='C')
pdf.cell(0, 10, "Mobile: +237 691796631 | Email: <EMAIL>", ln=True, align='C')
pdf.cell(0, 10, "LinkedIn: linkedin.com/in/antoine-didier-bayanga-710630116", ln=True, align='C')
pdf.ln(10)

# Summary
pdf.chapter_title("Summary")
summary_text = (
    "Skilled and dedicated full-stack developer with extensive experience in building customized web "
    "applications to solve real-world problems. Proficient in Python, FastAPI, JavaScript, VueJS, and PHP. "
    "Strong background in developing and enhancing e-commerce platforms and web applications."
)
pdf.chapter_body(summary_text)

# Professional Experience
pdf.chapter_title("Professional Experience")

# Toolbrothers Powertools GmbH
pdf.chapter_title("Toolbrothers Powertools GmbH")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "Full-stack Developer | October 2022 - Present | Göttingen, Germany", ln=True)
pdf.set_font("Arial", '', 12)
toolbrothers_text = (
    "- Enhanced CS-Cart-powered website functionality by creating new add-ons.\n"
    "- Utilized Python for backend development and data processing.\n"
    "- Built dynamic and interactive user interfaces using Vue.js.\n"
    "- Efficiently generated HTML templates using Jinja 2."
)
pdf.chapter_body(toolbrothers_text)

# DATICCUL
pdf.chapter_title("DATICCUL")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "Web Developer | September 2021 - October 2022 | Douala, Cameroon", ln=True)
pdf.set_font("Arial", '', 12)
daticcul_text = (
    "- Developed robust, scalable web applications using Symfony 5 and Laravel.\n"
    "- Ensured code reusability and maintainability, optimizing the development process."
)
pdf.chapter_body(daticcul_text)

# Emas
pdf.chapter_title("Emas")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "Computer Science Teacher | April 2022 - July 2022 | Yaoundé, Cameroon", ln=True)
pdf.set_font("Arial", '', 12)
emas_text = (
    "- Taught modules on operating systems and event-driven programming in VB.NET.\n"
    "- Provided hands-on experience with Linux systems and VB.NET for interactive applications."
)
pdf.chapter_body(emas_text)

# CIS
pdf.chapter_title("CIS")
pdf.set_font('DejaVu', 'I', 12)
pdf.cell(0, 10, "Computer Science Teacher | February 2018 - June 2022", ln=True)
pdf.set_font('DejaVu', '', 12)
cis_text = (
    "- Instructed students in Linux administration, networks and protocols, VOIP, advanced algorithms, "
    "and programming (Python, Java, C++).\n"
    "- Designed lesson plans, facilitated practical sessions, and evaluated student performance.\n"
    "- Served as an academic supervisor, providing mentorship and support."
)
pdf.chapter_body(cis_text)

# Particular Destiny Suites Hôtel
pdf.chapter_title("Particular Destiny Suites Hôtel")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "IT Manager | April 2021 - September 2021 | Douala, Cameroon", ln=True)
pdf.set_font("Arial", '', 12)
particular_destiny_text = (
    "- Managed IT equipment and administered the company's ERP system.\n"
    "- Ensured smooth operation of IT infrastructure."
)
pdf.chapter_body(particular_destiny_text)

# SIAPPHARMA
pdf.chapter_title("SIAPPHARMA")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "IT Manager | July 2018 - March 2021 | Douala, Cameroon", ln=True)
pdf.set_font("Arial", '', 12)
siappharma_text = (
    "- Led software development initiatives and managed IT infrastructure.\n"
    "- Administered the company's ERP system."
)
pdf.chapter_body(siappharma_text)

# Institut du Retour d'EXperience - IREX
pdf.chapter_title("Institut du Retour d'EXperience - IREX")
pdf.set_font("Arial", 'I', 12)
pdf.cell(0, 10, "Network Services Intern | October 2017 - January 2018 | Yaoundé, Cameroon", ln=True)
pdf.set_font("Arial", '', 12)
irex_text = (
    "- Deployed network services and enhanced network security.\n"
    "- Implemented DNS, DHCP, VPN services, and collaborative tools like Nextcloud and Zimbra."
)
pdf.chapter_body(irex_text)

# Education
pdf.chapter_title("Education")
education_text = "Université de Yaoundé 1\nBachelor’s Degree in Computer Science | 2012 - 2016"
pdf.chapter_body(education_text)

# Certifications
pdf.chapter_title("Certifications")
certifications_text = (
    "- JavaScript Algorithms and Data Structures\n"
    "- Responsive Web Design\n"
    "- Python (Basic)\n"
    "- Front End Libraries\n"
    "- Scientific Computing with Python"
)
pdf.chapter_body(certifications_text)

# Skills
pdf.chapter_title("Skills")
skills_text = (
    "- Programming Languages: Python, JavaScript, PHP\n"
    "- Frameworks and Libraries: FastAPI, Vue.js, Symfony, Laravel\n"
    "- Tools and Technologies: CS-Cart, Jinja 2, MariaDB, Pimcore, GraphQL API\n"
    "- Languages: French (Native), English (Fluent)"
)
pdf.chapter_body(skills_text)

# Save the PDF
pdf_output_path = "Antoine_Didier_Bayanga_Resume.pdf"
pdf.output(pdf_output_path)

print(f"PDF saved at {pdf_output_path}")