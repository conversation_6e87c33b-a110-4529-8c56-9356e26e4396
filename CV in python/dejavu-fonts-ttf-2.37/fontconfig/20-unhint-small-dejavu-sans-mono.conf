<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE fontconfig SYSTEM "../fonts.dtd">
<fontconfig>
  <!--  /etc/fonts/conf.d/20-unhint-small-dejavu-sans-mono.conf

        Disable hinting manually at smaller sizes (< 8ppem)
        This is a copy of the Bistream Vera fonts fonts rule, as DejaVu is
        derived from Vera.

	The Bistream Vera fonts have GASP entries suggesting that hinting be
	disabled below 8 ppem, but FreeType ignores those, preferring to use
	the data found in the instructed hints. The initial Vera release
	didn't include the right instructions in the 'prep' table.
 -->
  <match target="font">
    <test name="family">
      <string>DejaVu Sans Mono</string>
    </test>
    <test compare="less" name="pixelsize">
      <double>7.5</double>
    </test>
    <edit name="hinting">
      <bool>false</bool>
    </edit>
  </match>
</fontconfig>
