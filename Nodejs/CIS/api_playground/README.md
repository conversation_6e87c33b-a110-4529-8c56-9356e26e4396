# Simple Backend Demo

A simple Node.js backend application demonstrating RESTful API development with Express and SQLite.

## Features

- Express.js REST API
- SQLite database integration
- User management endpoints
- Professional project structure
- Database migrations system

## Prerequisites

- Node.js (v16 or higher)
- npm (Node Package Manager)

## Project Structure

```
project-root/
├── src/
│   ├── config/         # Configuration files
│   ├── controllers/    # Request handlers
│   ├── routes/         # API routes
│   ├── data/          # Database files
│   ├── db/            # Database setup and migrations
│   ├── app.js         # Express app setup
│   └── server.js      # Application entry point
├── .gitignore
├── package.json
└── README.md
```

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd simple-backend-demo
```

2. Install dependencies:
```bash
npm install
```

3. Initialize the database:
```bash
npm run db:setup
```

## Running the Application

Development mode (with auto-reload):
```bash
npm run dev
```

Production mode:
```bash
npm start
```

The server will start at `http://localhost:3000`.

## API Endpoints

### Users

- **GET /api/users**
  - Get all users
  - Returns: Array of user objects

- **POST /api/users**
  - Create a new user
  - Body:
    ```json
    {
      "name": "John Doe",
      "email": "<EMAIL>",
      "cardNumber": "****************",
      "cardExpiry": "12/24",
      "cardCvv": "123"
    }
    ```
  - Returns: Created user object

## API Documentation

### Users API

#### Create User
- **POST** `/api/users`
- **Request Body:**
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone_number": "+1234567890",
  "payment_info_card_number": "****************",
  "payment_info_card_holder": "John Doe",
  "payment_info_expiry": "12/25",
  "payment_info_cvv": "123"
}
```
- **Response:** 
```json
{
  "id": 1,
  "message": "User created successfully"
}
```

#### Get User
- **GET** `/api/users/id/:id`
- **Response:**
```json
{
  "id": 1,
  "first_name": "John",
  "last_name": "Doe",
  "email": "<EMAIL>",
  "phone_number": "+1234567890",
  "created_at": "2024-01-20T10:30:00Z",
  "updated_at": "2024-01-20T10:30:00Z"
}
```

#### Update User
- **PUT** `/api/users/:id`
- **Request Body:** (all fields optional)
```json
{
  "first_name": "Johnny",
  "email": "<EMAIL>"
}
```
- **Response:**
```json
{
  "message": "User updated successfully"
}
```

#### Delete User
- **DELETE** `/api/users/:id`
- **Response:**
```json
{
  "message": "User deleted successfully"
}
```

### Error Responses
All endpoints may return error responses in this format:
```json
{
  "error": "Error message description"
}
```

Common error status codes:
- `400`: Bad Request (invalid input)
- `404`: Not Found
- `500`: Server Error

## Scripts

- `npm start` - Start the application
- `npm run dev` - Start the application with nodemon
- `npm run db:setup` - Initialize database and create tables

## Development

### Adding New Tables

1. Create a new migration file in `src/db/migrations/`
2. Add the table creation SQL
3. Import and run the migration in `src/db/setup.js`
4. Run `npm run db:setup` to apply changes

### Adding New Routes

1. Create controller in `src/controllers/`
2. Create route file in `src/routes/`
3. Add route to `src/app.js`

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
