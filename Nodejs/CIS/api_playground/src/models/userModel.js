const db = require('../config/database');

const userModel = {
  create: (userData) => {
    return new Promise((resolve, reject) => {
      const {
        first_name,
        last_name,
        email,
        phone_number,
        payment_info_card_number,
        payment_info_card_holder,
        payment_info_expiry,
        payment_info_cvv
      } = userData;

      // Only store last 4 digits of card number
      const maskedCardNumber = '*'.repeat(12) + payment_info_card_number.slice(-4);

      db.run(
        `INSERT INTO users (
          first_name,
          last_name,
          email,
          phone_number,
          payment_info_card_number,
          payment_info_card_holder,
          payment_info_expiry,
          payment_info_cvv
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          first_name,
          last_name,
          email,
          phone_number,
          maskedCardNumber,
          payment_info_card_holder,
          payment_info_expiry,
          '***' // Never store actual CVV
        ],
        function(err) {
          if (err) reject(err);
          resolve(this.lastID);
        }
      );
    });
  },

  findAll: () => {
    return new Promise((resolve, reject) => {
      db.all(
        `SELECT 
          id,
          first_name,
          last_name,
          email,
          phone_number,
          payment_info_card_number,
          payment_info_card_holder,
          payment_info_expiry,
          payment_info_cvv,
          created_at,
          updated_at
        FROM users`,
        [],
        (err, rows) => {
          if (err) reject(err);
          resolve(rows);
        }
      );
    });
  },

  findById: (id) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT 
          id,
          first_name,
          last_name,
          email,
          phone_number,
          payment_info_card_number,
          payment_info_card_holder,
          payment_info_expiry,
          payment_info_cvv,
          created_at,
          updated_at
        FROM users 
        WHERE id = ?`,
        [id],
        (err, row) => {
          if (err) reject(err);
          resolve(row);
        }
      );
    });
  },

  update: (id, userData) => {
    return new Promise((resolve, reject) => {
      const {
        first_name,
        last_name,
        email,
        phone_number,
        payment_info_card_number,
        payment_info_card_holder,
        payment_info_expiry,
        payment_info_cvv
      } = userData;

      db.run(
        `UPDATE users SET 
          first_name = COALESCE(?, first_name),
          last_name = COALESCE(?, last_name),
          email = COALESCE(?, email),
          phone_number = COALESCE(?, phone_number),
          payment_info_card_number = COALESCE(?, payment_info_card_number),
          payment_info_card_holder = COALESCE(?, payment_info_card_holder),
          payment_info_expiry = COALESCE(?, payment_info_expiry),
          payment_info_cvv = COALESCE(?, payment_info_cvv),
          updated_at = DATETIME('now')
        WHERE id = ?`,
        [
          first_name,
          last_name,
          email,
          phone_number,
          payment_info_card_number,
          payment_info_card_holder,
          payment_info_expiry,
          payment_info_cvv,
          id
        ],
        function(err) {
          if (err) reject(err);
          resolve(this.changes);
        }
      );
    });
  },

  delete: (id) => {
    return new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM users WHERE id = ?',
        [id],
        function(err) {
          if (err) reject(err);
          resolve(this.changes);
        }
      );
    });
  },

  findByEmail: (email) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT 
          id,
          first_name,
          last_name,
          email,
          phone_number,
          payment_info_card_number,
          payment_info_card_holder,
          payment_info_expiry,
          payment_info_cvv,
          created_at,
          updated_at
        FROM users 
        WHERE email = ?`,
        [email],
        (err, row) => {
          if (err) reject(err);
          resolve(row);
        }
      );
    });
  },

  findByPhone: (phoneNumber) => {
    return new Promise((resolve, reject) => {
      db.get(
        `SELECT 
          id,
          first_name,
          last_name,
          email,
          phone_number,
          payment_info_card_number,
          payment_info_card_holder,
          payment_info_expiry,
          payment_info_cvv,
          created_at,
          updated_at
        FROM users 
        WHERE phone_number = ?`,
        [phoneNumber],
        (err, row) => {
          if (err) reject(err);
          resolve(row);
        }
      );
    });
  },

  checkDuplicates: async (email, phoneNumber, excludeId = null) => {
    return new Promise((resolve, reject) => {
      let query = 'SELECT email, phone_number FROM users WHERE email = ? OR phone_number = ?';
      const params = [email, phoneNumber];
      
      if (excludeId) {
        query += ' AND id != ?';
        params.push(excludeId);
      }

      db.get(query, params, (err, row) => {
        if (err) reject(err);
        resolve(row);
      });
    });
  }
};

module.exports = userModel;
