const fs = require('fs');
const path = require('path');
const initDatabase = require('./migrations/init');

const setupDatabase = async () => {
  try {
    // Create data directory if it doesn't exist
    const dataDir = path.resolve(__dirname, '../data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir);
    }

    await initDatabase();
    console.log('Database setup completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Database setup failed:', error);
    process.exit(1);
  }
};

setupDatabase();
