const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const validateUser = require('../middleware/validateUser');

// CREATE
router.post('/', validateUser, userController.createUser);

// READ
router.get('/', userController.getAllUsers);
router.get('/id/:id', userController.getUserById);
router.get('/email/:email', userController.getUserByEmail);
router.get('/phone/:phone', userController.getUserByPhone);

// UPDATE
router.put('/:id', validateUser, userController.updateUser);

// DELETE
router.delete('/:id', userController.deleteUser);

module.exports = router;
