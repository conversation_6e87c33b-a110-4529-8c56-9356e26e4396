const userModel = require('../models/userModel');

const validateUser = async (req, res, next) => {
  const { 
    first_name, 
    last_name, 
    email,
    phone_number,
    payment_info_card_number,
    payment_info_card_holder,
    payment_info_expiry,
    payment_info_cvv 
  } = req.body;

  const isCreating = req.method === 'POST';
  const isUpdating = req.method === 'PUT';

  // Validate required fields for creation
  if (isCreating) {
    if (!first_name || !last_name || !email) {
      return res.status(400).json({ error: 'Name and email are required' });
    }

    // Payment info is required for new users
    if (!payment_info_card_number || !payment_info_card_holder || 
        !payment_info_expiry || !payment_info_cvv) {
      return res.status(400).json({ error: 'All payment information is required for new users' });
    }
  }

  // For updates, only validate fields that are present
  if (isUpdating) {
    if (Object.keys(req.body).length === 0) {
      return res.status(400).json({ error: 'No fields provided for update' });
    }
    
    // If any payment field is provided, require all payment fields
    const hasPaymentFields = payment_info_card_number || payment_info_card_holder || 
                           payment_info_expiry || payment_info_cvv;
    
    if (hasPaymentFields) {
      if (!payment_info_card_number || !payment_info_card_holder || 
          !payment_info_expiry || !payment_info_cvv) {
        return res.status(400).json({ error: 'All payment information fields must be provided when updating payment details' });
      }
    }
  }

  // Validate email format if provided
  if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    return res.status(400).json({ error: 'Invalid email format' });
  }

  // Check for duplicates only when necessary
  try {
    const userId = req.params.id; // Will be undefined for POST requests
    
    if (isCreating) {
      // For new users, always check duplicates if email or phone is provided
      if (email || phone_number) {
        const duplicate = await userModel.checkDuplicates(email || null, phone_number || null);
        if (duplicate) {
          if (duplicate.email === email) {
            return res.status(400).json({ error: 'Email already in use' });
          }
          if (duplicate.phone_number === phone_number) {
            return res.status(400).json({ error: 'Phone number already in use' });
          }
        }
      }
    } else if (isUpdating) {
      // For updates, first get the current user data
      const currentUser = await userModel.findById(userId);
      if (!currentUser) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Only check for duplicates if email or phone is being changed
      if ((email && email !== currentUser.email) || 
          (phone_number && phone_number !== currentUser.phone_number)) {
        const duplicate = await userModel.checkDuplicates(
          email || null,
          phone_number || null,
          userId
        );
        
        if (duplicate) {
          if (email && duplicate.email === email) {
            return res.status(400).json({ error: 'Email already in use' });
          }
          if (phone_number && duplicate.phone_number === phone_number) {
            return res.status(400).json({ error: 'Phone number already in use' });
          }
        }
      }
    }
  } catch (err) {
    return res.status(500).json({ error: 'Error checking for duplicates' });
  }

  // Validate payment info if provided
  if (payment_info_card_number || isCreating) {
    // Remove spaces and dashes from card number
    const cleanCardNumber = payment_info_card_number.replace(/[\s-]/g, '');
    
    // Basic Luhn algorithm check for card number
    if (!/^\d{13,19}$/.test(cleanCardNumber) || !isValidLuhn(cleanCardNumber)) {
      return res.status(400).json({ error: 'Invalid card number' });
    }
  }

  if (payment_info_card_holder || isCreating) {
    // Card holder should be at least 2 words
    if (!/^[a-zA-Z]+ [a-zA-Z\s]+$/.test(payment_info_card_holder.trim())) {
      return res.status(400).json({ error: 'Invalid card holder name' });
    }
  }

  if (payment_info_expiry || isCreating) {
    // Expiry date format MM/YY
    if (!/^(0[1-9]|1[0-2])\/([0-9]{2})$/.test(payment_info_expiry)) {
      return res.status(400).json({ error: 'Invalid expiry date format (MM/YY)' });
    }

    // Check if card is not expired
    const [month, year] = payment_info_expiry.split('/');
    const expiry = new Date(2000 + parseInt(year), parseInt(month) - 1);
    const today = new Date();
    if (expiry < today) {
      return res.status(400).json({ error: 'Card has expired' });
    }
  }

  if (payment_info_cvv || isCreating) {
    // CVV should be 3 or 4 digits
    if (!/^\d{3,4}$/.test(payment_info_cvv)) {
      return res.status(400).json({ error: 'Invalid CVV' });
    }
  }

  next();
};

// Luhn algorithm for card number validation
function isValidLuhn(number) {
  let sum = 0;
  let isEven = false;
  
  // Loop through values starting from the rightmost digit
  for (let i = number.length - 1; i >= 0; i--) {
    let digit = parseInt(number.charAt(i));

    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }

    sum += digit;
    isEven = !isEven;
  }

  return sum % 10 === 0;
}

module.exports = validateUser;
