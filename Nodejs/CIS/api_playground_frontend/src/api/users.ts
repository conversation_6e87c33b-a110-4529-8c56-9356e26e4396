import apiClient from './client'
import { User } from '../types/user'

// Since the interceptor returns the data directly, we can use the raw type
export const getUsers = async (): Promise<User[]> => {
  try {
    const data = await apiClient.get<User[]>('/users')
    return Array.isArray(data) ? data : []
  } catch (error) {
    console.error('Error fetching users:', error)
    return []
  }
}

export const getUserById = async (id: number): Promise<User> => {
  try {
    const data = await apiClient.get<User>(`/users/id/${id}`)
    if (!data || Object.keys(data).length === 0) {
      throw new Error('User not found')
    }
    return data
  } catch (error) {
    throw error
  }
}

export const createUser = async (userData: User): Promise<User> => {
  try {
    const data = await apiClient.post<User>('/users', userData)
    if (!data || Object.keys(data).length === 0) {
      throw new Error('Failed to create user')
    }
    return data
  } catch (error) {
    throw error
  }
}

export const updateUser = async (id: number, userData: Partial<User>): Promise<User> => {
  try {
    const data = await apiClient.put<User>(`/users/${id}`, userData)
    if (!data || Object.keys(data).length === 0) {
      throw new Error('Failed to update user')
    }
    return data
  } catch (error) {
    throw error
  }
}

export const deleteUser = async (id: number): Promise<void> => {
  try {
    await apiClient.delete(`/users/${id}`)
  } catch (error) {
    throw error
  }
}