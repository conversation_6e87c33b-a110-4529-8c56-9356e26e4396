import { Modu<PERSON> } from '@nestjs/common';
import { AssetsDisposalsService } from './assets-disposals.service';
import { AssetsDisposalsController } from './assets-disposals.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AssetsDisposalEntity } from './entities/assets-disposal.entity';
// import { UsersModule } from '../users/users.module';
import { AssetsModule } from '../assets/assets.module';
import { ExercisesModule } from '../exercises/exercises.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([AssetsDisposalEntity]),
    // UsersModule,
    AssetsModule,
    ExercisesModule,
  ],
  controllers: [AssetsDisposalsController],
  providers: [AssetsDisposalsService],
})
export class AssetsDisposalsModule {}
