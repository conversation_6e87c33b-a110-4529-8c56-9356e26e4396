import { AssetEntity } from '../../../modules/assets/entities/asset.entity';
import { DefaultEntity } from '../../../utils/entities/default.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { ExerciseEntity } from '../../../modules/exercises/entities/exercise.entity';
import { BusinessUnitEntity } from '../../../modules/business-units/entities/business-unit.entity';
import { UserEntity } from '../../../modules/users/entities/user.entity';

export class BaseAssetActionEntity extends DefaultEntity {
  @ManyToOne(() => ExerciseEntity, (e) => e.id, {
    eager: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'exercise_id' })
  exercise: ExerciseEntity;

  @Column('enum', {
    enum: ['waiting', 'validated', 'unvalidated'],
    nullable: true,
    default: 'waiting',
  })
  status: 'waiting' | 'validated' | 'unvalidated';

  @ManyToOne(() => BusinessUnitEntity, (bu) => bu.id, {
    eager: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'business_unit_id' })
  businessUnit?: BusinessUnitEntity;

  @ManyToOne(() => UserEntity, (u) => u.id, {
    eager: false,
    nullable: true,
    onDelete: 'SET NULL',
  })
  createdBy?: UserEntity;

  @ManyToOne(() => UserEntity, (u) => u.id, {
    eager: false,
    nullable: true,
    onDelete: 'SET NULL',
  })
  validatedBy?: UserEntity;
}

@Entity('assets_disposals')
export class AssetsDisposalEntity extends BaseAssetActionEntity {
  @ManyToOne(() => AssetEntity, (a) => a.id, {
    eager: false,
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'asset_id' })
  asset: AssetEntity;

  @Column('enum', { enum: ['m', 'c'], nullable: true, default: 'm' })
  type: 'm' | 'c';

  @Column('decimal', { name: 'disposal_price', nullable: true })
  disposalPrice: number | null;

  @Column({ type: 'date', name: 'disposal_date' })
  disposalDate: Date;
}
