import { BadRequestException, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { CreateAssetsDisposalDto } from './dto/create-assets-disposal.dto';
import { UpdateAssetsDisposalDto } from './dto/update-assets-disposal.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, In, Repository } from 'typeorm';
import { AssetsDisposalEntity } from './entities/assets-disposal.entity';
import { AssetsService } from '../assets/assets.service';
import { AssetEntity } from '../assets/entities/asset.entity';
import { AssetsMovementsService } from '../assets-movements/assets-movements.service';
import { UserEntity } from '../users/entities/user.entity';
import { CreateAssetsMovementDto } from '../assets-movements/dto/create-assets-movement.dto';

@Injectable()
export class AssetsDisposalsService {
  private readonly logger = new Logger(AssetsDisposalsService.name)
  constructor(
    @InjectRepository(AssetsDisposalEntity)
    private readonly repository: Repository<AssetsDisposalEntity>,
    private readonly assetsService: AssetsService,
    private readonly assetsMovementsService: AssetsMovementsService,
    private readonly dataSource: DataSource,
  ) { }

  // async create(payload: CreateAssetsDisposalDto) {
  //   const a = new AssetsDisposalEntity();
  //   Object.assign(a, { ...payload });
  //   const asset = await this.assetsService.findOne(payload.asset as any);
  //   asset.disposalDate = new Date(payload.disposalDate);
  //   await this.assetsService.save(asset);
  //   return this.repository.save(a);
  // }


  async create(
    payload: CreateAssetsDisposalDto,
    currentUser: UserEntity, // Add current user for movement creation
  ) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Fetch the asset
      const asset = await queryRunner.manager
        .createQueryBuilder(AssetEntity, 'asset')
        .leftJoinAndSelect('asset.office', 'office')
        .leftJoinAndSelect('asset.store', 'store')
        .where('asset.id = :assetId', { assetId: payload.asset as unknown as number })
        .getOne();
      if (!asset) {
        throw new NotFoundException('Asset not found');
      }
      
      // Validate that the asset is not already disposed of
      const existingDisposal = await queryRunner.manager
        .createQueryBuilder(AssetsDisposalEntity, 'disposal')
        .leftJoinAndSelect('disposal.asset', 'asset')
        .where('asset.id = :assetId', { assetId: asset.id })
        .getOne();

      if (existingDisposal) {
        throw new BadRequestException('Asset is already disposed of');
      }

      // Create the disposal record
      const disposal = new AssetsDisposalEntity();
      Object.assign(disposal, { ...payload });
      disposal.asset = asset;
      disposal.disposalDate = new Date(payload.disposalDate);
      disposal.businessUnit = asset.businessUnit;

      const savedDisposal = await queryRunner.manager.save(disposal);

      // Update the asset's state
      asset.physicalCondition = 'out-of-service';
      asset.functionalCondition = 'out-of-service';
      await queryRunner.manager.save(asset);


      // Create an "out" movement for the asset
      const movementPayload: CreateAssetsMovementDto = {
        asset: asset.id, // ID of the asset being disposed of
        ipDate: new Date(payload.disposalDate), // Use the disposal date as the movement date
        status: 'done', // Set the movement status to "done"
        destinationType: 'none', // No destination type for "out" movements
        movementType: 'out', // Set the movement type to "out"
        exercise: savedDisposal.exercise.id, // Use the asset's exercise ID
        sourceOffice: asset.office?.id, // Set the source office if the asset is in an office
        sourceStore: asset.store?.id, // Set the source store if the asset is in a store
      };

      await this.assetsMovementsService.createOutMovement(
        movementPayload,
        currentUser,
        queryRunner,
      );

      // Commit the transaction
      await queryRunner.commitTransaction();

      return savedDisposal;
    } catch (error) {
      // Rollback the transaction in case of an error
      await queryRunner.rollbackTransaction();
      this.logger.log(error)
      this.logger.log(error.stack)
      throw error;
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }


  findAll() {
    return this.repository.find({ order: { updatedAt: 'DESC' } });
  }

  async findOne(id: number) {
    const check = await this.repository.findOneBy({ id });

    if (!check) {
      throw new NotFoundException();
    }
    return check;
  }

  async findOneByBarcode(barcode: string) {
    const check = await this.repository.findOneBy({ barcode });
    if (!!check) {
      return check;
    }
    throw new NotFoundException();
  }

  async update(id: number, payload: UpdateAssetsDisposalDto) {
    const check = await this.repository.findOneBy({ id });

    if (!check) {
      throw new NotFoundException();
    }
    Object.assign(check, { ...payload });
    return this.repository.save(check);
  }

  async remove(id: number) {
    const check = await this.repository.findOneBy({ id });

    if (!check) {
      throw new NotFoundException();
    }
    return this.repository.remove(check);
  }

  public async deleteMany(ids: number[]): Promise<void> {
    this.repository.delete({
      id: In(ids),
    });
  }

  public async findByAsset(id: number) {
    return this.repository.find({
      where: { asset: { id } },
      order: { updatedAt: 'DESC' },
    });
  }

  public save(d: AssetsDisposalEntity) {
    return this.repository.save(d);
  }
}
