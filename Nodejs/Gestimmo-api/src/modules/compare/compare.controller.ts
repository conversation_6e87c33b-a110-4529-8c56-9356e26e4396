import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { CompareService } from './compare.service';
import { ApiBearerAuth, ApiQuery, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Abilities } from '../auth/decorators/abilities.decorator';
import { AbilityGuard } from '../auth/guards/abilities.guard';
import { BilanDto } from './dto/billan.dto';

@Controller('compare')
@Controller('compare')
@ApiTags('compare')
@ApiBearerAuth('access-token')
@UseGuards(JwtAuthGuard)
export class CompareController {
  constructor(private readonly service: CompareService) {}

  @Get('inventories/:id1/:id2')
  @UseGuards(AbilityGuard)
  @Abilities('ReadInventoriesPrograms')
  @ApiResponse({
    status: 200,
    isArray: true,
    type: BilanDto,
  })
  compare(@Param('id1') id1: string, @Param('id2') id2: string) {
    return this.service.compareInventories(+id1, +id2);
  }
}
