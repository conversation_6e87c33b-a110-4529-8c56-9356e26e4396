import { OfficeEntity } from '../../offices/entities/office.entity';
import { AssetEntity } from '../../assets/entities/asset.entity';
import { InventoriesProgramEntity } from '../../inventories-programs/entities/inventories-program.entity';
import { ApiProperty } from '@nestjs/swagger';
import { DefaultAssetResponse } from '../../assets/dto/create-asset.dto';

export class BilanDto {
  @ApiProperty()
  inventory: InventoriesProgramEntity;

  @ApiProperty({ type: DefaultAssetResponse, isArray: true })
  recensedAssets: (AssetEntity & {
    latestOffice: OfficeEntity;
  })[];
}
