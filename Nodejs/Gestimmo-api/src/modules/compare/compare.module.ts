import { Module } from '@nestjs/common';
import { CompareService } from './compare.service';
import { CompareController } from './compare.controller';
import { AssetsModule } from '../assets/assets.module';
import { OfficesModule } from '../offices/offices.module';
import { InventoriesProgramsModule } from '../inventories-programs/inventories-programs.module';
// import { UsersModule } from '../users/users.module';
import { ExercisesModule } from '../exercises/exercises.module';
import { AssetsMovementsModule } from '../assets-movements/assets-movements.module';

@Module({
  controllers: [CompareController],
  providers: [CompareService],
  imports: [
    AssetsModule,
    OfficesModule,
    InventoriesProgramsModule,
    // UsersModule,
    ExercisesModule,
    AssetsMovementsModule,
  ],
})
export class CompareModule {}
