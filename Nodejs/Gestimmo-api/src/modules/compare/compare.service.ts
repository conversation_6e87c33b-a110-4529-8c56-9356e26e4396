import { HttpException, Injectable } from '@nestjs/common';
import { OfficesService } from '../offices/offices.service';
import { AssetsService } from '../assets/assets.service';
import { InventoriesProgramsService } from '../inventories-programs/inventories-programs.service';
import { AssetsStatesService } from '../assets/assets-state.service';
import { AssetsMovementsService } from '../assets-movements/assets-movements.service';

@Injectable()
export class CompareService {
  constructor(
    private readonly officeService: OfficesService,
    private readonly assetsMovementsService: AssetsMovementsService,
    private readonly assetsService: AssetsService,
    private readonly inventoriesProgramsService: InventoriesProgramsService,
    private readonly assetsStatesService: AssetsStatesService,
  ) {}

  async compareInventories(id1: number, id2: number) {
    const programs = await this.inventoriesProgramsService.findByIds([
      id1,
      id2,
    ]);

    if (programs.length !== 2) {
      throw new HttpException('An inventory not found', 404);
    }

    const [program1, program2] = programs;

    const inventoried1 = await this.getAssetsByStatus(
      program1.id,
      'inventoried',
    );
    const inventoried2 = await this.getAssetsByStatus(
      program2.id,
      'inventoried',
    );

    return [
      {
        inventory: program1,
        recensedAssets: inventoried1,
      },
      {
        inventory: program2,
        recensedAssets: inventoried2,
      },
    ];
  }

  private async getAssetsByStatus(
    programId: number,
    status: 'inventoried' | 'losed' | 'waiting',
  ) {
    const assetsStatus =
      await this.assetsStatesService.getAssetsStatusByProgram(
        programId,
        status,
      );
    return assetsStatus.map((assetStatus) => ({
      ...assetStatus.asset,
      latestOffice: assetStatus.office,
    }));
  }
}
