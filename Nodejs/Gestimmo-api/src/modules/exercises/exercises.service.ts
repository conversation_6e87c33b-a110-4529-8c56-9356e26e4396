import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { CreateExerciseDto } from './dto/create-exercise.dto';
import { UpdateExerciseDto } from './dto/update-exercise.dto';
import { DataSource, In, Repository } from 'typeorm';
import { ExerciseEntity } from './entities/exercise.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { AmortizationPeriod } from '../../utils/amortization';
import { AssetEntity } from '../assets/entities/asset.entity';
import { AmortizationSheduleService } from '../assets/amortization-shedule.service';
import { AmortizationSheduleEntity } from '../assets/entities/amortization-shedule.entity';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { AccountsService } from '../accounts/accounts.service';
import { NotificationsEntity } from '../notifications/entities/notifications.entity';
import { NotificationTypes } from '../notifications/notifications.types';
import { AssetStateEntity } from '../assets/entities/asset-state.entity';

@Injectable()
export class ExercisesService {
  constructor(
    @InjectRepository(ExerciseEntity)
    private exerciseRepository: Repository<ExerciseEntity>,
    @InjectRepository(AssetEntity)
    private assetsRepository: Repository<AssetEntity>,
    @InjectRepository(NotificationsEntity)
    private notifRepo: Repository<NotificationsEntity>,
    private readonly amortizationSheduleService: AmortizationSheduleService,
    private readonly accountsService: AccountsService,
    private readonly dataSource: DataSource,
  ) { }

  async create(payload: CreateExerciseDto) {
    const existingRegionByName = await this.exerciseRepository.findOneBy({
      code: payload.code,
    });

    if (existingRegionByName) {
      throw new BadRequestException('Exercise whit code already exists');
    }
    const c = new ExerciseEntity();
    Object.assign(c, { ...payload });
    const result = await this.exerciseRepository.save(c);
    if (result.current) {
      await this.makeAsCurrent(result.id);
    }
    return result;
  }

  findAll() {
    return this.exerciseRepository.find({
      order: {
        updatedAt: 'DESC',
      },
    });
  }

  async findOne(id: number) {
    const e = await this.exerciseRepository.findOneBy({ id });
    if (!e) throw new NotFoundException('Exercise not found');
    return e;
  }

  async findOneByBarcode(barcode: string) {
    const check = await this.exerciseRepository.findOneBy({ barcode });
    if (!!check) {
      return check;
    }
    throw new NotFoundException();
  }

  async update(id: number, payload: UpdateExerciseDto) {
    const check = await this.exerciseRepository.preload({
      id,
      ...payload,
    });

    if (!check) {
      throw new NotFoundException('Exercise not found');
    }

    if (check.current) {
      return this.makeAsCurrent(check.id);
    }

    return this.exerciseRepository.save(check);
  }

  async remove(id: number) {
    const check = await this.findOne(id);
    if (!check) {
      throw new NotFoundException('Exercise not found');
    }

    return this.exerciseRepository.remove(check);
  }

  public async deleteMany(ids: number[]): Promise<void> {
    this.exerciseRepository.delete({
      id: In(ids),
    });
  }

  public async getCurrentExercise() {
    return this.exerciseRepository.findOneBy({ current: true });
  }

  private async makeAsCurrent(id: number) {
    const check = await this.findOne(id);
    if (!check) {
      throw new NotFoundException('Exercise not found');
    }
    await this.exerciseRepository.update({ current: true }, { current: false });
    check.current = true;
    check.closed = false;
    return this.exerciseRepository.save(check);
  }

  async closeExercise(id: number, date: Date, period: AmortizationPeriod) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const exercise = await queryRunner.manager.findOne(ExerciseEntity, {
        where: { id },
      });

      if (!exercise) {
        throw new NotFoundException(`Exercise with ID ${id} not found.`);
      }

      if (exercise.closed) {
        throw new BadRequestException(
          `Exercise with ID ${id} is already closed.`,
        );
      }

      // 1. Perform other tasks related to closing the exercise
      // ...

      // 2. Get all assets that need to be amortized for this exercise
      const assets = await this.getAssetsToAmortize(exercise);

      // 3. Trigger amortization computation for each asset
      const amortizationPromises = [];
      for (const asset of assets) {
        amortizationPromises.push(
          this.amortizationSheduleService.computeAmortization({
            asset,
            date,
            exercise,
            period,
          }),
        );
      }

      // Resolve all promises and handle individual errors
      const results = await Promise.allSettled(amortizationPromises);
      const errors = results.filter((result) => result.status === 'rejected');

      if (errors.length > 0) {
        console.error('Errors during amortization computations:', errors);
        // Handle errors appropriately, e.g., log them, notify admins, etc.
      }

      // 4. Update the exercise status to closed
      await queryRunner.manager.update(ExerciseEntity, id, {
        closed: true,
      } as QueryDeepPartialEntity<ExerciseEntity>);

      // 5. Perform any other tasks after closing the exercise
      // ...

      // Commit the transaction
      await queryRunner.commitTransaction();

      const notification = new NotificationsEntity();
      notification.message = `L'exercise ${exercise.label} vien d'etre cloturer`;
      notification.type = NotificationTypes.exercise;
      await this.notifRepo.save(notification);

      return { message: `Exercise ${id} closed successfully.` };
    } catch (error) {
      // Roll back the transaction if any operation fails
      await queryRunner.rollbackTransaction();
      console.error('Error closing exercise:', error);
      throw new InternalServerErrorException('Failed to close exercise.');
    } finally {
      // Release the query runner
      await queryRunner.release();
    }
  }

  private async getAssetsToAmortize(
    exercise: ExerciseEntity,
  ): Promise<AssetEntity[]> {
    // Build a subquery to select the latest asset state (based on createdAt) for each asset.
    const latestAssetStateSubquery = this.assetsRepository.manager
      .createQueryBuilder(AssetStateEntity, 'latest_asset_state')
      .select([
        'latest_asset_state.asset_id AS asset_id',
        'MAX(latest_asset_state.createdAt) AS max_created_at',
      ])
      .groupBy('latest_asset_state.asset_id')
      .getQuery();

    return this.assetsRepository
      .createQueryBuilder('asset')
      // Join using the correct relation name: inventoriesStatuses
      .leftJoinAndSelect('asset.inventoriesStatuses', 'assetState')
      .innerJoin(
        `(${latestAssetStateSubquery})`,
        'latest_states',
        'assetState.asset_id = latest_states.asset_id AND assetState.createdAt = latest_states.max_created_at'
      )
      // Continue joining other related entities
      .leftJoinAndSelect('asset.amortizationShedules', 'schedule')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('category.assetAccountNumber', 'assetAccount')
      .leftJoinAndSelect('category.amortizationAccountNumber', 'amortizationAccount')
      .leftJoinAndSelect('category.endowmentAccountNumber', 'endowmentAccount')
      // Filter assets belonging to the provided exercise.
      .where('asset.exercise_id = :exerciseId', { exerciseId: exercise.id })
      // Only include assets that are validated.
      .andWhere('asset.status = :status', { status: 'validated' })
      // Ensure that the latest asset state is 'inventoried'.
      .andWhere('assetState.status = :assetStateStatus', {
        assetStateStatus: 'inventoried',
      })
      // Ensure the asset is in service.
      .andWhere('asset.physical_condition <> :outOfService', {
        outOfService: 'out-of-service',
      })
      .andWhere('asset.functional_condition <> :outOfService', {
        outOfService: 'out-of-service',
      })
      // Exclude assets that already have an amortization schedule for this exercise.
      .andWhere((qb) => {
        const subQuery = qb
          .subQuery()
          .select('1')
          .from(AmortizationSheduleEntity, 'existing_schedule')
          .where('existing_schedule.asset_id = asset.id')
          .andWhere('existing_schedule.exercise_id = :exerciseId')
          .getQuery();
        return `NOT EXISTS (${subQuery})`;
      })
      .setParameter('exerciseId', exercise.id)
      .getMany();
  }


  async reopenExercise(id: number) {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const exercise = await queryRunner.manager.findOne(ExerciseEntity, {
        where: { id },
      });

      if (!exercise) {
        throw new NotFoundException(`Exercise with ID ${id} not found.`);
      }

      if (!exercise.closed) {
        throw new BadRequestException(`Exercise with ID ${id} is not closed.`);
      }

      // 1. Handle Amortization Records (Option B: Invalidate)
      const amortizationShedules = await queryRunner.manager.find(
        AmortizationSheduleEntity,
        {
          where: { exercise: { id: exercise.id } },
          relations: [
            'asset',
            'asset.category',
            'amortizationAccount',
            'endowmentAccount',
          ],
        },
      );

      for (const schedule of amortizationShedules) {
        schedule.isValid = false; // Add 'isValid' field to AmortizationSheduleEntity
        await queryRunner.manager.save(schedule);

        // 2. Revert Account Balances (Option A: Reverse)
        const amortizationAccount = schedule.amortizationAccount;
        const endowmentAccount = schedule.endowmentAccount;

        amortizationAccount.addToBalance(-schedule.amortizationAmount); // Subtract amount
        endowmentAccount.addToBalance(-schedule.amortizationAmount); // Subtract amount

        await this.accountsService.updateBalance(
          amortizationAccount.id,
          amortizationAccount.balance,
        );
        await this.accountsService.updateBalance(
          endowmentAccount.id,
          endowmentAccount.balance,
        );
      }

      // 3. Update the exercise status to open
      exercise.closed = false;
      await queryRunner.manager.save(exercise);

      // 4. Perform any other tasks after reopening the exercise
      // ... (e.g., update asset status, send notifications, etc.)

      await queryRunner.commitTransaction();
      return { message: `Exercise ${id} reopened successfully.` };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('Error reopening exercise:', error);
      throw new InternalServerErrorException('Failed to reopen exercise.');
    } finally {
      await queryRunner.release();
    }
  }
}
