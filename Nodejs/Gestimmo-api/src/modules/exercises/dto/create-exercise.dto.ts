import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsDate, IsNumber, IsString } from 'class-validator';
import { DefaultDTO } from '../../../utils/dto/Default.dto';

export class CreateExerciseDto extends DefaultDTO {
  @ApiProperty()
  @IsString()
  readonly code: string;

  @ApiProperty()
  @IsNumber()
  readonly label: number;

  @ApiProperty()
  @IsBoolean()
  readonly current: boolean;

  @ApiProperty()
  @IsBoolean()
  readonly closed: boolean;

  @ApiProperty()
  @IsDate()
  readonly startDate: Date;

  @ApiProperty()
  @IsDate()
  readonly endDate: Date;
}

export class DefaultExerciseResponse extends CreateExerciseDto {
  @ApiProperty()
  readonly id: number;

  @ApiProperty()
  readonly createdAt: Date;

  @ApiProperty()
  readonly updatedAt: Date;
}
