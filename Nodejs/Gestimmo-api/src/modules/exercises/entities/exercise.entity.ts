import { Column, Entity, OneToMany } from 'typeorm';
import { DefaultEntity } from '../../../utils/entities/default.entity';
import { SiteEntity } from '../../../modules/sites/entities/site.entity';

@Entity('exercises')
export class ExerciseEntity extends DefaultEntity {
  @Column({ unique: true, nullable: true })
  code: string;

  @Column({ type: 'int', nullable: true, default: 2023 })
  label: number;

  @Column()
  current: boolean;

  @Column()
  closed: boolean;

  @Column()
  startDate: Date;

  @Column()
  endDate: Date;

  @OneToMany(() => SiteEntity, (object) => object.exercise, {
    eager: false,
  })
  sites: SiteEntity[];
}
