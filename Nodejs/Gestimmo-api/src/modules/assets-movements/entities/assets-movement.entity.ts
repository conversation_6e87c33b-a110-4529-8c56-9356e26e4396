import { AssetEntity } from '../../../modules/assets/entities/asset.entity';
import { DefaultEntity } from '../../../utils/entities/default.entity';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { OfficeEntity } from '../../../modules/offices/entities/office.entity';
import { ExerciseEntity } from '../../../modules/exercises/entities/exercise.entity';
import { UserEntity } from '../../../modules/users/entities/user.entity';
import { StoreEntity } from '../../../modules/stores/entities/store.entity';

export type MovementDestinationType = 'office' | 'store' | 'none';

export type MovementStatus = 'pending' | 'done' | 'returned';

export type MovementType = 'in' | 'out' | 'transfer';

@Entity('assets_movements')
export class AssetsMovementEntity extends DefaultEntity {
  @ManyToOne(() => AssetEntity, (a) => a.movements, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'asset_id' })
  asset: AssetEntity;

  @ManyToOne(() => OfficeEntity, (o) => o.id, {
    onDelete: 'SET NULL',
    nullable: true,
  })
  @JoinColumn({ name: 'source_office_id' })
  sourceOffice: OfficeEntity | null;

  @ManyToOne(() => StoreEntity, (s) => s.id, {
    onDelete: 'SET NULL',
    nullable: true,
  })
  @JoinColumn({ name: 'source_store_id' })
  sourceStore: StoreEntity | null;

  @ManyToOne(() => OfficeEntity, (o) => o.id, {
    onDelete: 'SET NULL',
    nullable: true,
  })
  @JoinColumn({ name: 'destination_office_id' })
  destinationOffice: OfficeEntity | null;

  @ManyToOne(() => StoreEntity, (s) => s.id, {
    onDelete: 'SET NULL',
    nullable: true,
  })
  @JoinColumn({ name: 'destination_store_id' })
  destinationStore: StoreEntity | null;

  @Column({ type: 'date', name: 'ip_date', default: () => 'CURRENT_TIMESTAMP' })
  ipDate: Date;

  @ManyToOne(() => ExerciseEntity, (e) => e.id, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'exercise_id' })
  exercise: ExerciseEntity;

  @Column('enum', {
    enum: ['pending', 'done', 'returned'],
    nullable: false,
    default: 'pending',
  })
  status: MovementStatus;

  @Column('enum', {
    enum: ['office', 'store', 'none'],
    nullable: false,
    name: 'destination_type',
    default: 'store',
  })
  destinationType: MovementDestinationType;

  @Column('enum', {
    enum: ['in', 'out', 'transfer'],
    nullable: false,
    name: 'movement_type',
    default: 'in',
  })
  movementType: MovementType;

  @ManyToOne(() => UserEntity, (u) => u.id, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  createdBy?: UserEntity;

  @ManyToOne(() => UserEntity, (u) => u.id, {
    nullable: true,
    onDelete: 'SET NULL',
  })
  validatedBy?: UserEntity;
}
