import { AssetEntity } from '../../../modules/assets/entities/asset.entity';
import { ExerciseEntity } from '../../../modules/exercises/entities/exercise.entity';
import { OfficeEntity } from '../../../modules/offices/entities/office.entity';
import { StoreEntity } from '../../../modules/stores/entities/store.entity';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  ValidateIf,
} from 'class-validator';
import { DefaultDTO } from '../../../utils/dto/Default.dto';
import {
  MovementDestinationType,
  MovementStatus,
  MovementType,
} from '../entities/assets-movement.entity';

export class CreateAssetsMovementDto extends DefaultDTO {
  @ApiProperty({ required: true, type: () => Number })
  @IsNumber()
  @IsNotEmpty()
  asset: number;

  @ApiPropertyOptional({ type: () => Number })
  @IsOptional()
  @IsNumber()
  sourceOffice?: number;

  @ApiPropertyOptional({ type: () => Number })
  @IsOptional()
  @IsNumber()
  sourceStore?: number;

  @ApiPropertyOptional({ type: () => Number })
  @ValidateIf((o) => o.destinationType === 'office')
  @IsNumber()
  destinationOffice?: number;

  @ApiPropertyOptional({ type: () => Number })
  @ValidateIf((o) => o.destinationType === 'store')
  @IsNumber()
  destinationStore?: number;

  @ApiProperty({ required: true, type: () => Number })
  @IsNumber()
  @IsNotEmpty()
  exercise: number;

  @ApiProperty({ default: () => new Date() })
  @IsOptional()
  @IsDate()
  ipDate?: Date;

  @ApiProperty({
    enum: ['pending', 'done', 'returned'],
    default: 'pending',
  })
  @IsEnum(['pending', 'done', 'returned'])
  status: MovementStatus;

  @ApiProperty({ enum: ['office', 'store', 'none'], required: true })
  @IsEnum(['office', 'store', 'none'])
  @IsNotEmpty()
  destinationType: MovementDestinationType;

  @ApiProperty({ enum: ['in', 'out', 'transfer'], required: true })
  @IsEnum(['in', 'out', 'transfer'])
  @IsNotEmpty()
  movementType: MovementType;
}

export class DefaultAssetsMovementResponse extends CreateAssetsMovementDto {
  @ApiProperty()
  readonly id: number;

  @ApiProperty()
  readonly createdAt: Date;

  @ApiProperty()
  readonly updatedAt: Date;

  @ApiPropertyOptional({ type: () => Number })
  readonly createdById?: number;

  @ApiPropertyOptional({ type: () => Number })
  readonly validatedById?: number;
}

export class PaginatedAssetsMovementResponse {
  @ApiProperty({ type: () => [DefaultAssetsMovementResponse] })
  readonly items: DefaultAssetsMovementResponse[]

  @ApiProperty()
  readonly total: number
}