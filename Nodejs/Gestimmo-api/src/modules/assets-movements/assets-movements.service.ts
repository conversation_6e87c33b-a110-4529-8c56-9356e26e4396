import {
  BadRequestException,
  ConflictException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { CreateAssetsMovementDto, DefaultAssetsMovementResponse } from './dto/create-assets-movement.dto';
import { UpdateAssetsMovementDto } from './dto/update-assets-movement.dto';
import { AssetsMovementEntity, MovementDestinationType } from './entities/assets-movement.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, In, QueryRunner, Repository } from 'typeorm';
import { UpdateSiteDto } from '../sites/dto/update-site.dto';
import { ExercisesService } from '../exercises/exercises.service';
import { OfficesService } from '../offices/offices.service';
import { StoresService } from '../stores/stores.service';
import { MovementStatus, MovementType } from './entities/assets-movement.entity';
import { UserEntity } from '../users/entities/user.entity';
import { AssetEntity } from '../assets/entities/asset.entity';

@Injectable()
export class AssetsMovementsService {
  private readonly logger = new Logger(AssetsMovementsService.name)
  constructor(
    @InjectRepository(AssetsMovementEntity)
    private readonly repository: Repository<AssetsMovementEntity>,
    private readonly exerciseService: ExercisesService,
    private readonly officesService: OfficesService,
    private readonly storeService: StoresService,
    private readonly dataSource: DataSource,
  ) { }

  async create(
    payload: CreateAssetsMovementDto,
    currentUser: UserEntity,
    queryRunner?: QueryRunner,
  ): Promise<AssetsMovementEntity> {
    let manager = queryRunner ? queryRunner.manager : null;
    let shouldCommit = false;

    if (!manager) {
      const newQueryRunner = this.dataSource.createQueryRunner();
      await newQueryRunner.connect();
      await newQueryRunner.startTransaction();
      manager = newQueryRunner.manager;
      queryRunner = newQueryRunner;
      shouldCommit = true;
    }

    try {
      const asset = await manager.findOne(AssetEntity, {
        where: { id: payload.asset },
        relations: ['exercise'],
      });
      if (!asset) {
        throw new NotFoundException('Asset not found');
      }

      const isFirstMovement =
        (await manager.count(AssetsMovementEntity, {
          where: { asset: { id: asset.id } },
        })) === 0;

      let exercise;
      if (payload.exercise) {
        exercise = await this.exerciseService.findOne(payload.exercise);
      } else {
        exercise = await this.exerciseService.getCurrentExercise();
      }
      if (!exercise || !exercise.id) {
        throw new NotFoundException('Exercise not found');
      }

      let sourceOffice = null;
      if (payload.sourceOffice) {
        sourceOffice = await this.officesService.findOne(
          payload.sourceOffice.toString(),
        );
        if (!sourceOffice) {
          throw new BadRequestException('Source office not found');
        }
      }

      let sourceStore = null;
      if (payload.sourceStore) {
        sourceStore = await this.storeService.findOne(payload.sourceStore);
        if (!sourceStore) {
          throw new BadRequestException('Source store not found');
        }
      }

      // Use destination from payload if provided
      let destinationOffice = null;
      if (payload.destinationType === 'office' && payload.destinationOffice) {
        destinationOffice = await this.officesService.findOne(
          payload.destinationOffice.toString(),
        );
        if (!destinationOffice) {
          throw new BadRequestException('Destination office not found');
        }
      }

      let destinationStore = null;
      if (payload.destinationType === 'store' && payload.destinationStore) {
        destinationStore = await this.storeService.findOne(
          payload.destinationStore,
        );
        if (!destinationStore) {
          throw new BadRequestException('Destination store not found');
        }
      }

      // Validation based on destinationType
      if (payload.destinationType === 'office' && !destinationOffice) {
        throw new BadRequestException(
          'Destination office is required for office destination type',
        );
      }
      if (payload.destinationType === 'store' && !destinationStore) {
        throw new BadRequestException(
          'Destination store is required for store destination type',
        );
      }

      const movementType = isFirstMovement ? 'in' : 'transfer';

      // Create movement with provided destination
      const movement = manager.create(AssetsMovementEntity, {
        asset,
        sourceOffice,
        sourceStore,
        destinationOffice,
        destinationStore,
        exercise,
        ipDate: payload.ipDate || new Date(),
        status: payload.status || 'pending',
        destinationType: payload.destinationType, // Use the provided destinationType
        movementType,
        createdBy: currentUser,
      });

      // Only validate 'in' movements if they are not the first movement
      if (movementType !== 'in') {
        movement.validatedBy = currentUser;
      }

      // No need to override destination for first movement anymore

      const savedMovement = await manager.save(movement);

      // Update asset location based on the actual destinationType
      if (movement.destinationType === 'store') {
        asset.store = movement.destinationStore;
        asset.office = null;
      } else if (movement.destinationType === 'office') {
        asset.office = movement.destinationOffice;
        asset.store = null;
      }
      await manager.save(asset);

      if (shouldCommit) {
        await queryRunner.commitTransaction();
      }

      return savedMovement;
    } catch (error) {
      if (shouldCommit) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      if (shouldCommit) {
        await queryRunner.release();
      }
    }
  }

  async createOutMovement(
    payload: CreateAssetsMovementDto,
    currentUser: UserEntity,
    queryRunner?: QueryRunner,
  ): Promise<AssetsMovementEntity> {
    let manager = queryRunner ? queryRunner.manager : null;
    let shouldCommit = false;
  
    if (!manager) {
      const newQueryRunner = this.dataSource.createQueryRunner();
      await newQueryRunner.connect();
      await newQueryRunner.startTransaction();
      manager = newQueryRunner.manager;
      queryRunner = newQueryRunner;
      shouldCommit = true;
    }
  
    try {
      // Fetch the asset
      const asset = await manager
        .createQueryBuilder(AssetEntity, 'asset')
        .leftJoinAndSelect('asset.exercise', 'exercise')
        .leftJoinAndSelect('asset.office', 'office')
        .leftJoinAndSelect('asset.store', 'store')
        .where('asset.id = :assetId', { assetId: payload.asset })
        .getOne();
      if (!asset) {
        throw new NotFoundException('Asset not found');
      }
  
      // Validate that the asset is currently in a location (office or store)
      if (!asset.office && !asset.store) {
        throw new BadRequestException('Asset is not assigned to any location');
      }
  
      // Fetch the exercise
      let exercise;
      if (payload.exercise) {
        exercise = await this.exerciseService.findOne(payload.exercise);
      } else {
        exercise = await this.exerciseService.getCurrentExercise();
      }
      if (!exercise || !exercise.id) {
        throw new NotFoundException('Exercise not found');
      }
  
      // Set the source location based on the asset's current location
      const sourceOffice = asset.office;
      const sourceStore = asset.store;
  
      // Create the movement
      const movement = manager.create(AssetsMovementEntity, {
        asset,
        sourceOffice,
        sourceStore,
        destinationOffice: null, // No destination for "out" movements
        destinationStore: null, // No destination for "out" movements
        exercise,
        ipDate: payload.ipDate || new Date(),
        status: payload.status || 'done', // Default to "done" for out movements
        destinationType: 'none',
        movementType: 'out', // Set movement type to "out"
        createdBy: currentUser,
        validatedBy: currentUser, // Automatically validate out movements
      });
  
      // Save the movement
      const savedMovement = await manager.save(movement);
  
      // Update the asset's location to null (since it's being moved out)
      asset.office = null;
      asset.store = null;
  
      // Update the asset's physical and functional condition to "out-of-service"
      asset.physicalCondition = 'out-of-service';
      asset.functionalCondition = 'out-of-service';
  
      // Save the updated asset
      await manager.save(asset);
  
      if (shouldCommit) {
        await queryRunner.commitTransaction();
      }
  
      return savedMovement;
    } catch (error) {
      if (shouldCommit) {
        await queryRunner.rollbackTransaction();
      }
      throw error;
    } finally {
      if (shouldCommit) {
        await queryRunner.release();
      }
    }
  }

  async findAll({ page, limit }: { page: number; limit: number }) {
    const queryBuilder = this.repository.createQueryBuilder('movement')
      .orderBy('movement.updatedAt', 'DESC')
      .leftJoinAndSelect('movement.asset', 'asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('movement.sourceOffice', 'sourceOffice')
      .leftJoinAndSelect('movement.sourceStore', 'sourceStore')
      .leftJoinAndSelect('movement.destinationOffice', 'destinationOffice')
      .leftJoinAndSelect('movement.destinationStore', 'destinationStore')
      .leftJoinAndSelect('movement.createdBy', 'createdBy')
      .leftJoinAndSelect('movement.validatedBy', 'validatedBy');

    if (limit > 0) {
      queryBuilder.skip((page - 1) * limit).take(limit);
    }

    const [items, total] = await queryBuilder.getManyAndCount();
    return { items, total };
  }

  async findOne(id: number): Promise<AssetsMovementEntity> {
    const movement = await this.repository.createQueryBuilder('movement')
      .where('movement.id = :id', { id })
      .leftJoinAndSelect('movement.asset', 'asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('movement.sourceOffice', 'sourceOffice')
      .leftJoinAndSelect('movement.sourceStore', 'sourceStore')
      .leftJoinAndSelect('movement.destinationOffice', 'destinationOffice')
      .leftJoinAndSelect('movement.destinationStore', 'destinationStore')
      .leftJoinAndSelect('movement.createdBy', 'createdBy')
      .leftJoinAndSelect('movement.validatedBy', 'validatedBy')
      .getOne();

    if (!movement) {
      throw new NotFoundException(`Movement with ID ${id} not found`);
    }
    return movement;
  }

  async findOneByBarcode(barcode: string): Promise<AssetsMovementEntity> {
    const movement = await this.repository.createQueryBuilder('movement')
      .where('movement.barcode = :barcode', { barcode })
      .leftJoinAndSelect('movement.asset', 'asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('movement.sourceOffice', 'sourceOffice')
      .leftJoinAndSelect('movement.sourceStore', 'sourceStore')
      .leftJoinAndSelect('movement.destinationOffice', 'destinationOffice')
      .leftJoinAndSelect('movement.destinationStore', 'destinationStore')
      .leftJoinAndSelect('movement.createdBy', 'createdBy')
      .leftJoinAndSelect('movement.validatedBy', 'validatedBy')
      .getOne();

    if (!movement) {
      throw new NotFoundException(`Movement with barcode ${barcode} not found`);
    }
    return movement;
  }
  
  async update(
    id: number,
    payload: UpdateAssetsMovementDto,
    currentUser: UserEntity,
  ): Promise<AssetsMovementEntity> {
    return this.dataSource.transaction(async (manager: EntityManager) => {
      const movement = await manager.findOne(AssetsMovementEntity, {
        where: { id },
        relations: ['asset', 'asset.office', 'asset.store'],
      });

      if (!movement) {
        throw new NotFoundException(`Movement with ID ${id} not found`);
      }

      if (movement.movementType === 'in') {
        throw new BadRequestException(
          `Movement with type 'in' cannot be updated`,
        );
      }

      // Update fields based on payload
      if (payload.status) {
        movement.status = payload.status;
      }

      // If the movement is being set to 'done' and there's a validatedBy user, update it
      if (payload.status === 'done' && !movement.validatedBy) {
        movement.validatedBy = currentUser;
      }

      // Save the updated movement
      return await manager.save(movement);
    });
  }

  async remove(
    id: number,
  ): Promise<{
    movement: AssetsMovementEntity;
    asset: AssetEntity;
    previousMovement: AssetsMovementEntity;
  }> {
    return this.dataSource.transaction(async (manager: EntityManager) => {
      const movement = await manager.findOne(AssetsMovementEntity, {
        where: { id },
        relations: ['asset', 'asset.office', 'asset.store'],
      });

      if (!movement) {
        throw new NotFoundException(`Movement with ID ${id} not found`);
      }

      // If this movement was the last one for the asset, revert the asset's location
      const isLastMovement = await this.isLastMovementForAsset(
        manager,
        movement.asset.id,
        id,
      );
      let previousMovement = null;
      if (isLastMovement) {
        const asset = await manager.findOne(AssetEntity, {
          where: { id: movement.asset.id },
          relations: ['office', 'store'],
        });
        // Revert to the previous location or set to null if no previous movements
        previousMovement = await this.findPreviousMovement(
          manager,
          movement.asset.id,
          id,
        );
        if (previousMovement) {
          if (previousMovement.destinationType === 'office') {
            asset.office = previousMovement.destinationOffice;
            asset.store = null;
          } else if (previousMovement.destinationType === 'store') {
            asset.store = previousMovement.destinationStore;
            asset.office = null;
          }
        } else {
          // No previous movement, set both to null
          asset.office = null;
          asset.store = null;
        }
        // Delete the movement
        await manager.remove(movement);
        return { movement, asset, previousMovement };
      }
      // Delete the movement
      await manager.remove(movement);
      return { movement, asset: null, previousMovement };
    });
  }

  private async isLastMovementForAsset(
    manager: EntityManager,
    assetId: number,
    movementId: number,
  ): Promise<boolean> {
    const movements = await manager.find(AssetsMovementEntity, {
      where: { asset: { id: assetId } },
      order: { ipDate: 'DESC' },
    });

    return movements.length > 0 && movements[0].id === movementId;
  }

  private async findPreviousMovement(
    manager: EntityManager,
    assetId: number,
    currentMovementId: number,
  ): Promise<AssetsMovementEntity | undefined> {
    const movements = await manager.find(AssetsMovementEntity, {
      where: { asset: { id: assetId } },
      order: { ipDate: 'DESC' },
    });

    const currentMovementIndex = movements.findIndex(
      (m) => m.id === currentMovementId,
    );
    return movements[currentMovementIndex + 1];
  }

  public async deleteMany(ids: number[]): Promise<void> {
    this.repository.delete({
      id: In(ids),
    });
  }

  async findByAsset(id: number): Promise<AssetsMovementEntity[]> {
    const movements = await this.repository.createQueryBuilder('movement')
      .leftJoinAndSelect('movement.asset', 'asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('movement.sourceOffice', 'sourceOffice')
      .leftJoinAndSelect('movement.sourceStore', 'sourceStore')
      .leftJoinAndSelect('movement.destinationOffice', 'destinationOffice')
      .leftJoinAndSelect('movement.destinationStore', 'destinationStore')
      .leftJoinAndSelect('movement.exercise', 'exercise')
      .leftJoinAndSelect('movement.createdBy', 'createdBy')
      .leftJoinAndSelect('movement.validatedBy', 'validatedBy')
      .where('asset.id = :id', { id })
      .orderBy('movement.updatedAt', 'DESC')
      .getMany();
  
    return movements;
  }

  public async findByExerciseRange({
    exercise_n0_label,
    exercise_n1_label,
    date_n0,
    date_n1,
    limit,
    page,
  }: {
    exercise_n0_label: number;
    exercise_n1_label: number;
    date_n0: string;
    date_n1: string;
    page: number;
    limit: number;
  }) {
    const query = this.repository
      .createQueryBuilder('a')
      .leftJoinAndSelect('a.exercise', 'e')
      .leftJoinAndSelect('a.asset', 'asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('a.sourceOffice', 'sourceOffice')
      .leftJoinAndSelect('a.sourceStore', 'sourceStore')
      .leftJoinAndSelect('a.destinationOffice', 'destinationOffice')
      .leftJoinAndSelect('a.destinationStore', 'destinationStore')
      .leftJoinAndSelect('a.createdBy', 'createdBy')
      .leftJoinAndSelect('a.validatedBy', 'validatedBy')
      .where(
        'e.label >= :exercise_n0_label AND e.label <= :exercise_n1_label',
        { exercise_n0_label, exercise_n1_label },
      )
      .andWhere('a.ip_date >= :date_n0 AND a.ip_date <= :date_n1', {
        date_n0,
        date_n1,
      });
    if (!isNaN(page) && !isNaN(limit) && limit > 0) {
      query.skip((page - 1) * limit).take(limit);
    }

    const [items, count] = await query.getManyAndCount();
    return { items, count };
  }

  public async save(d: AssetsMovementEntity) {
    if (!d.exercise) {
      d.exercise = await this.exerciseService.getCurrentExercise();
    }
    return this.repository.save(d);
  }

  public async findMovementsByExercises(ids: number[]) {
    return this.repository.find({
      where: [
        {
          exercise: {
            id: In(ids),
          },
        },
        {
          status: 'done',
        },
      ],
    });
  }

  // New Utility Methods:


  /**
   * Get movements by movement type.
   */
  async findByMovementType(
    movementType: MovementType,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ items: DefaultAssetsMovementResponse[]; total: number }> {
    const queryBuilder = this.repository
      .createQueryBuilder('movement')
      .leftJoinAndSelect('movement.asset', 'asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('movement.sourceOffice', 'sourceOffice')
      .leftJoinAndSelect('movement.sourceStore', 'sourceStore')
      .leftJoinAndSelect('movement.destinationOffice', 'destinationOffice')
      .leftJoinAndSelect('movement.destinationStore', 'destinationStore')
      .leftJoinAndSelect('movement.createdBy', 'createdBy')
      .leftJoinAndSelect('movement.validatedBy', 'validatedBy')
      .where('movement.movementType = :movementType', { movementType });

    if (limit > 0) {
      queryBuilder.skip((page - 1) * limit).take(limit);
    }

    const [items, total] = await queryBuilder.getManyAndCount();

    const responseItems = items.map((item) =>
      this.transformToDto(item),
    );

    return { items: responseItems, total };
  }

  /**
   * Get movements by status.
   */
  async findByStatus(
    status: MovementStatus,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ items: DefaultAssetsMovementResponse[]; total: number }> {
    const queryBuilder = this.repository
      .createQueryBuilder('movement')
      .leftJoinAndSelect('movement.asset', 'asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('movement.sourceOffice', 'sourceOffice')
      .leftJoinAndSelect('movement.sourceStore', 'sourceStore')
      .leftJoinAndSelect('movement.destinationOffice', 'destinationOffice')
      .leftJoinAndSelect('movement.destinationStore', 'destinationStore')
      .leftJoinAndSelect('movement.createdBy', 'createdBy')
      .leftJoinAndSelect('movement.validatedBy', 'validatedBy')
      .where('movement.status = :status', { status });

    if (limit > 0) {
      queryBuilder.skip((page - 1) * limit).take(limit);
    }

    const [items, total] = await queryBuilder.getManyAndCount();

    const responseItems = items.map((item) =>
      this.transformToDto(item),
    );

    return { items: responseItems, total };
  }

  /**
   * Get movements by destination type.
   */
  async findByDestinationType(
    destinationType: MovementDestinationType,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ items: DefaultAssetsMovementResponse[]; total: number }> {
    const queryBuilder = this.repository
      .createQueryBuilder('movement')
      .leftJoinAndSelect('movement.asset', 'asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('movement.sourceOffice', 'sourceOffice')
      .leftJoinAndSelect('movement.sourceStore', 'sourceStore')
      .leftJoinAndSelect('movement.destinationOffice', 'destinationOffice')
      .leftJoinAndSelect('movement.destinationStore', 'destinationStore')
      .leftJoinAndSelect('movement.createdBy', 'createdBy')
      .leftJoinAndSelect('movement.validatedBy', 'validatedBy')
      .where('movement.destinationType = :destinationType', {
        destinationType,
      });

    if (limit > 0) {
      queryBuilder.skip((page - 1) * limit).take(limit);
    }

    const [items, total] = await queryBuilder.getManyAndCount();

    const responseItems = items.map((item) =>
      this.transformToDto(item),
    );

    return { items: responseItems, total };
  }

  /**
   * Get movements for a specific office.
   */
  async findByOffice(
    officeId: number,
    type: 'source' | 'destination',
    page: number = 1,
    limit: number = 10,
  ): Promise<{ items: DefaultAssetsMovementResponse[]; total: number }> {
    if (!officeId) {
          throw new BadRequestException('Office ID is required');
        }

    const queryBuilder = this.repository
      .createQueryBuilder('movement')
      .leftJoinAndSelect('movement.asset', 'asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('movement.sourceOffice', 'sourceOffice')
      .leftJoinAndSelect('movement.sourceStore', 'sourceStore')
      .leftJoinAndSelect('movement.destinationOffice', 'destinationOffice')
      .leftJoinAndSelect('movement.destinationStore', 'destinationStore')
      .leftJoinAndSelect('movement.createdBy', 'createdBy')
      .leftJoinAndSelect('movement.validatedBy', 'validatedBy');

    if (type === 'source') {
      queryBuilder.where('sourceOffice.id = :officeId', { officeId });
    } else {
      queryBuilder.where('destinationOffice.id = :officeId', {
        officeId,
      });
    }

    if (limit > 0) {
      queryBuilder.skip((page - 1) * limit).take(limit);
    }

    const [items, total] = await queryBuilder.getManyAndCount();

    const responseItems = items.map((item) =>
      this.transformToDto(item),
    );

    return { items: responseItems, total };
  }

  /**
   * Get movements for a specific store.
   */
  async findByStore(
    storeId: number,
    type: 'source' | 'destination',
    page: number = 1,
    limit: number = 10,
  ): Promise<{ items: DefaultAssetsMovementResponse[]; total: number }> {
    const queryBuilder = this.repository
      .createQueryBuilder('movement')
      .leftJoinAndSelect('movement.asset', 'asset')
      .leftJoinAndSelect('asset.category', 'category')
      .leftJoinAndSelect('movement.sourceOffice', 'sourceOffice')
      .leftJoinAndSelect('movement.sourceStore', 'sourceStore')
      .leftJoinAndSelect('movement.destinationOffice', 'destinationOffice')
      .leftJoinAndSelect('movement.destinationStore', 'destinationStore')
      .leftJoinAndSelect('movement.createdBy', 'createdBy')
      .leftJoinAndSelect('movement.validatedBy', 'validatedBy');

    if (type === 'source') {
      queryBuilder.where('sourceStore.id = :storeId', { storeId });
    } else {
      queryBuilder.where('destinationStore.id = :storeId', {
        storeId,
      });
    }

    if (limit > 0) {
      queryBuilder.skip((page - 1) * limit).take(limit);
    }

    const [items, total] = await queryBuilder.getManyAndCount();

    const responseItems = items.map((item) =>
      this.transformToDto(item),
    );

    return { items: responseItems, total };
  }

  private transformToDto(
    entity: AssetsMovementEntity,
  ): DefaultAssetsMovementResponse {
    const dto = new DefaultAssetsMovementResponse();
    Object.assign(dto, entity);
    return dto;
  }
}