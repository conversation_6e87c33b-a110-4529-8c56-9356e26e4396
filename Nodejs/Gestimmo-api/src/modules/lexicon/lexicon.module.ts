
import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LexiconController } from './lexicon.controller';
import { LexiconService } from './lexicon.service';
import { LexiconEntryEntity } from './entities/lexicon.entity';

@Global()
@Module({
  imports: [TypeOrmModule.forFeature([LexiconEntryEntity])],
  controllers: [LexiconController],
  providers: [LexiconService],
  exports: [LexiconService]
})
export class LexiconModule {}
