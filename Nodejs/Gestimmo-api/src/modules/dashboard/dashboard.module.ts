import { Modu<PERSON> } from '@nestjs/common';
import { DashboardService } from './dashboard.service';
import { DashboardController } from './dashboard.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AccountsModule } from '../accounts/accounts.module';
import { AssetEntity } from '../assets/entities/asset.entity';
import { ExercisesModule } from '../exercises/exercises.module';
import { OfficesModule } from '../offices/offices.module';
// import { UsersModule } from '../users/users.module';
import { AssetsDisposalEntity } from '../assets-disposals/entities/assets-disposal.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([AssetEntity, AssetsDisposalEntity]),
    // UsersModule,
    AccountsModule,
    OfficesModule,
    ExercisesModule,
  ],
  controllers: [DashboardController],
  providers: [DashboardService],
})
export class DashboardModule {}
