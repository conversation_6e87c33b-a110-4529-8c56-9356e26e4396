import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { AssetEntity } from '../assets/entities/asset.entity';
import { ExercisesService } from '../exercises/exercises.service';
import { AssetsDisposalEntity } from '../assets-disposals/entities/assets-disposal.entity';

@Injectable()
export class DashboardService {
  constructor(
    @InjectRepository(AssetEntity)
    private readonly assetRepository: Repository<AssetEntity>,

    @InjectRepository(AssetsDisposalEntity)
    private readonly disposalsRepository: Repository<AssetsDisposalEntity>,
    private readonly exerciseService: ExercisesService,
  ) {}

  async statistics(exerciseId: number = undefined) {
    const assetsTotalValue = (await this.assetsTotalValue(exerciseId)) || 0;
    const assetsTotalCount = (await this.assetsTotalCount(exerciseId)) || 0;
    const acquiredAssetsTotalValue =
      (await this.acquiredAssetsTotalValue(exerciseId)) || 0;
    const assetsdisposalsTotalValue =
      (await this.assetsdisposalsTotalValue(exerciseId)) || 0;
    const assetsdisposalsCount =
      (await this.assetsdisposalsCount(exerciseId)) || 0;
    return {
      assetsTotalValue,
      acquiredAssetsTotalValue,
      assetsdisposalsTotalValue,
      assetsdisposalsCount,
      assetsTotalCount,
    };
  }

  private async assetsTotalValue(exerciseId: number = undefined) {
    const queryBuilder = this.assetRepository.createQueryBuilder('asset');

    if (!isNaN(exerciseId) && exerciseId !== undefined) {
      queryBuilder.where('asset.exercise_id = :exerciseId', { exerciseId });
    }

    const result = await queryBuilder
      .select('SUM(asset.purchasePrice)', 'totalValue')
      .getRawOne();

    return result.totalValue;
  }

  private async acquiredAssetsTotalValue(exerciseId: number = undefined) {
    const queryBuilder = this.assetRepository.createQueryBuilder('asset');

    if (!isNaN(exerciseId) && exerciseId !== undefined) {
      queryBuilder.where('asset.exercise_id = :exerciseId', { exerciseId });
    }

    const result = await queryBuilder
      .select(
        'SUM(CASE WHEN asset.is_acquired = true THEN asset.purchasePrice ELSE 0 END)',
        'totalValue',
      )
      .getRawOne();

    return result.totalValue;
  }

  private async assetsdisposalsTotalValue(exerciseId: number = undefined) {
    const queryBuilder =
      this.disposalsRepository.createQueryBuilder('disposals');

    if (!isNaN(exerciseId) && exerciseId !== undefined) {
      queryBuilder.where('disposals.exercise_id = :exerciseId', { exerciseId });
    }

    const result = await queryBuilder
      .select('SUM(disposals.disposalPrice)', 'totalValue')
      .getRawOne();

    return result.totalValue;
  }

  private async assetsdisposalsCount(exerciseId: number = undefined) {
    const queryBuilder =
      this.disposalsRepository.createQueryBuilder('disposals');

    if (!isNaN(exerciseId) && exerciseId !== undefined) {
      queryBuilder.where('disposals.exercise_id = :exerciseId', { exerciseId });
    }

    const result = await queryBuilder.getCount();

    return result;
  }

  private async assetsTotalCount(exerciseId: number = undefined) {
    const queryBuilder = this.assetRepository.createQueryBuilder('assets');

    if (!isNaN(exerciseId) && exerciseId !== undefined) {
      queryBuilder.where('assets.exercise_id = :exerciseId', { exerciseId });
    }

    const result = await queryBuilder.getCount();

    return result;
  }
}
