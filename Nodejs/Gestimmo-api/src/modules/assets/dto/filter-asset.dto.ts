import { ApiProperty } from '@nestjs/swagger';
import {
  IsIn,
  IsNotEmpty,
  IsOptional,
  IsString,
  ValidateNested,
  IsNumber,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';

export class FilterCriterionDto {
  @ApiProperty({ example: 'barcode', description: 'Field to filter on' })
  @IsString()
  @IsNotEmpty()
  field: string;

  @ApiProperty({
    example: 'superieur',
    description: 'Comparison operator (superieur, egal, inferieur)',
  })
  @IsString()
  @IsNotEmpty()
  @IsIn(['superieur', 'egal', 'inferieur', 'sous chaine de'])
  compare_operator: 'superieur' | 'egal' | 'inferieur' | 'sous chaine de';

  @ApiProperty({ example: '12345', description: 'Value to compare against' })
  @IsNotEmpty()
  compare_value: string | number | boolean;
}

export class SortByDto {
  @ApiProperty({ example: 'category', description: 'Field to sort by' })
  @IsString()
  @IsNotEmpty()
  field: 'category' | 'site' | 'purchaseDate';

  @ApiProperty({ example: 'ASC', description: 'Sort direction' })
  @IsString()
  @IsNotEmpty()
  @IsIn(['ASC', 'DESC'])
  direction: 'ASC' | 'DESC';
}

export class FilterAssetDto {
  @ApiProperty({
    type: [FilterCriterionDto],
    description: 'Array of filter criteria',
    required: false,
  })
  @IsOptional()
  @ValidateNested({ each: true })
  @Type(() => FilterCriterionDto)
  filters?: FilterCriterionDto[];

  @ApiProperty({
    type: SortByDto,
    description: 'Sorting criteria',
    required: false,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => SortByDto)
  sortBy?: SortByDto;
}
