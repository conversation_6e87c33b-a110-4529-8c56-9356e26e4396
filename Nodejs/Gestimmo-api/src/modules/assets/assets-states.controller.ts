import {
  Controller,
  Post,
  Body,
  UseGuards,
  Param,
  Get,
  Query,
} from '@nestjs/common';

import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AbilityGuard } from '../auth/guards/abilities.guard';
import { Abilities } from '../auth/decorators/abilities.decorator';
import { UsersService } from '../users/services/users.service';
import { CreateAssetStateDto } from './dto/assets-states/create-asset-state.dto';
import { AssetsStatesService } from './assets-state.service';
import { DefaultAssetResponse } from './dto/create-asset.dto';
import { UpdateAssetStateDto } from './dto/assets-states/update-asset-state.dto';

@Controller('assets-states')
@ApiTags('assets')
@ApiBearerAuth('access-token')
@UseGuards(JwtAuthGuard)
export class AssetsStatesController {
  constructor(
    private readonly service: AssetsStatesService,
    private readonly userService: UsersService,
  ) { }

  @Post('/:assetId/inventoring-states')
  @UseGuards(AbilityGuard)
  @Abilities('EditAssets')
  @ApiResponse({
    type: DefaultAssetResponse,
    status: 201,
  })
  async create(
    @Body() payload: CreateAssetStateDto,
    @Param('assetId') assetId: string,
  ) {
    return this.service.updateOrCreateAssetState(payload);
  }

  @Post('/:programId/sync-assets-withprogram')
  @UseGuards(AbilityGuard)
  @Abilities('EditAssets')
  async syncAssetsWihPrograms(@Param('programId') programId: string) {
    return this.service.syncAssetsWihPrograms(+programId);
  }

  @Post('/mark-as-inventoried')
  @UseGuards(AbilityGuard)
  @Abilities('EditAssets')
  @ApiResponse({
    type: DefaultAssetResponse,
    status: 201,
  })
  @ApiBody({ type: UpdateAssetStateDto })
  async markAsInventoried(@Body() payload: UpdateAssetStateDto) {
    return this.service.markAsInventoried(payload);
  }


  @Get('by-status/filtered')
  @ApiResponse({
    type: DefaultAssetResponse,
    status: 200,
    isArray: true,
  })
  @UseGuards(AbilityGuard)
  @ApiQuery({ name: 'exerciseId', type: 'string', required: false })
  @ApiQuery({
    name: 'status',
    type: 'string',
    required: false,
    enum: ['waiting', 'losed', 'inventoried'],
  })
  @Abilities('ReadAssets')
  async getAssetsByStatus(
    @Query('status') status: 'waiting' | 'losed' | 'inventoried',
    @Query('exerciseId') exerciseId: string,
  ) {
    return this.service.getAssetsByStatus({ status, exerciseId: +exerciseId });
  }

  @Get('amortizables/exercice/:exerciseId')
  @UseGuards(AbilityGuard)
  @Abilities('ReadAssets')
  @ApiResponse({
    status: 201,
    type: DefaultAssetResponse,
  })
  @ApiQuery({ name: 'page', type: 'string', required: false })
  @ApiQuery({ name: 'search', type: 'string', required: false })
  @ApiQuery({ name: 'category_id', type: 'string', required: false })
  @ApiQuery({ name: 'bu', type: 'string', required: false })
  @ApiQuery({
    name: 'type',
    enum: ['straight-line', 'reducing-balance'],
    type: 'string',
    required: false,
  })
  @ApiOperation({
    summary: 'Get paginated assets amortizable in given xercise',
  })
  @ApiQuery({
    name: 'limit',
    type: 'string',
    required: false,
    description: '-1 for all items in one page',
  })
  async getAmortizables(
    @Query('category_id') category_id: string,
    @Query('bu') bu: string,
    @Query('page') page = '1',
    @Query('limit') limit = '100',
    @Query('search') search = '',
    @Query('type') type: 'straight-line' | 'reducing-balance' | null,

    @Param('exerciseId') exerciseId: string,
  ) {
    return this.service.getAmortizableAssets({
      page: +page,
      limit: +limit,
      exerciseId: +exerciseId,
      buId: +bu,
      categoryId: +category_id,
      type,
      search,
    });
  }
}
