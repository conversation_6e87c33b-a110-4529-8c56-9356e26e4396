import { Injectable } from '@nestjs/common';
import { AssetEntity } from './entities/asset.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { ExercisesService } from '../exercises/exercises.service';

@Injectable()
export class AssetsDashboardService {
  constructor(
    @InjectRepository(AssetEntity)
    private readonly repository: Repository<AssetEntity>,
    private readonly exerciseService: ExercisesService,
  ) {}

  async getDisposedAssets(exerciceId?: number) {
    const q: SelectQueryBuilder<AssetEntity> =
      this.repository.createQueryBuilder('asset');

    if (!exerciceId) {
      q.where(
        `asset.id IN (SELECT asset_id FROM assets_disposals WHERE status = 'validated')`,
      );
    } else {
      q.where(
        `asset.id IN (SELECT asset_id FROM assets_disposals WHERE status = 'validated' AND exercise_id = :exerciceId)`,
        { exerciceId },
      );
    }

    const data = await q.distinct(true).getMany();

    return data;
  }
}
