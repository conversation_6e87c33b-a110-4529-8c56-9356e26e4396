import { Module } from '@nestjs/common';
import { CategoriesService } from './categories.service';
import { CategoriesController } from './categories.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CategoryEntity } from './entities/category.entity';
import { ComponentEntity } from './entities/component.entity';
// import { UsersModule } from '../users/users.module';
import { BusinessUnitsModule } from '../business-units/business-units.module';
import { AccountsModule } from '../accounts/accounts.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([CategoryEntity, ComponentEntity]),
    // UsersModule,
    BusinessUnitsModule,
    AccountsModule,
  ],
  controllers: [CategoriesController],
  providers: [CategoriesService],
  exports: [CategoriesService],
})
export class CategoriesModule {}
