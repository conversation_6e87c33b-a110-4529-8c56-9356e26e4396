import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { CategoryEntity } from './entities/category.entity';
import { In, Repository } from 'typeorm';
import { ComponentEntity } from './entities/component.entity';
import { UserEntity } from '../users/entities/user.entity';

@Injectable()
export class CategoriesService {
  constructor(
    @InjectRepository(CategoryEntity)
    private repository: Repository<CategoryEntity>,

    @InjectRepository(ComponentEntity)
    private componentRepository: Repository<ComponentEntity>,
  ) {}

  async create(payload: CreateCategoryDto) {
    const existingItemByName = await this.repository.findOneBy({
      label: payload.label,
    });

    const existingItemByRef = await this.repository.findOneBy({
      reference: payload.reference,
    });

    if (existingItemByName || existingItemByRef) {
      throw new BadRequestException(
        'Category with name on reference already exists',
      );
    }
    const category = new CategoryEntity();
    Object.assign(category, { ...payload });
    const savedCategory = await this.repository.save(category);

    const components = payload.components.map((componentDto) => {
      const component = this.componentRepository.create({
        ...componentDto,
        category: savedCategory,
      });
      return component;
    });

    const savedComponents = await this.componentRepository.save(components);

    savedCategory.components = savedComponents;
    await this.repository.save(savedCategory);

    return this.findOne(savedCategory.id);
  }

  findAll() {
    return this.repository.createQueryBuilder('category')
      .leftJoinAndSelect('category.assetAccountNumber', 'assetAccountNumber')
      .leftJoinAndSelect('category.endowmentAccountNumber', 'endowmentAccountNumber')
      .leftJoinAndSelect('category.amortizationAccountNumber', 'amortizationAccountNumber')
      .leftJoinAndSelect('category.businessUnit', 'businessUnit')
      .leftJoinAndSelect('category.components', 'components')
      .leftJoinAndSelect('category.parent', 'parent')
      .leftJoinAndSelect('category.lexiconEntries', 'lexiconEntries')
      .orderBy('category.updatedAt', 'DESC')
      .getMany();
  }

  async findOne(id: number) {
    const category = await this.repository.createQueryBuilder('category')
      .leftJoinAndSelect('category.assetAccountNumber', 'assetAccountNumber')
      .leftJoinAndSelect('category.endowmentAccountNumber', 'endowmentAccountNumber')
      .leftJoinAndSelect('category.amortizationAccountNumber', 'amortizationAccountNumber')
      .leftJoinAndSelect('category.businessUnit', 'businessUnit')
      .leftJoinAndSelect('category.components', 'components')
      .leftJoinAndSelect('category.parent', 'parent')
      .leftJoinAndSelect('category.lexiconEntries', 'lexiconEntries')
      .where('category.id = :id', { id })
      .getOne();

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return category;
  }

  async findOneByBarcode(barcode: string) {
    const category = await this.repository.createQueryBuilder('category')
      .leftJoinAndSelect('category.assetAccountNumber', 'assetAccountNumber')
      .leftJoinAndSelect('category.endowmentAccountNumber', 'endowmentAccountNumber')
      .leftJoinAndSelect('category.amortizationAccountNumber', 'amortizationAccountNumber')
      .leftJoinAndSelect('category.businessUnit', 'businessUnit')
      .leftJoinAndSelect('category.components', 'components')
      .leftJoinAndSelect('category.parent', 'parent')
      .leftJoinAndSelect('category.lexiconEntries', 'lexiconEntries')
      .where('category.barcode = :barcode', { barcode })
      .getOne();

    if (!category) {
      throw new NotFoundException();
    }
    return category;
  }

  async update(id: number, payload: UpdateCategoryDto) {
    const category = await this.repository.createQueryBuilder('category')
      .leftJoinAndSelect('category.assetAccountNumber', 'assetAccountNumber')
      .leftJoinAndSelect('category.endowmentAccountNumber', 'endowmentAccountNumber')
      .leftJoinAndSelect('category.amortizationAccountNumber', 'amortizationAccountNumber')
      .leftJoinAndSelect('category.businessUnit', 'businessUnit')
      .leftJoinAndSelect('category.components', 'components')
      .leftJoinAndSelect('category.parent', 'parent')
      .leftJoinAndSelect('category.lexiconEntries', 'lexiconEntries')
      .where('category.id = :id', { id })
      .getOne();

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    Object.assign(category, payload);

    if (payload.components) {
      const updatedComponents = await Promise.all(
        payload.components.map(async (componentDto) => {
          let component: ComponentEntity;

          if (componentDto.id) {
            component = await this.componentRepository.findOneBy({
              id: componentDto.id,
            });

            if (component) {
              Object.assign(component, componentDto);
              return this.componentRepository.save(component);
            }
          } else {
            component = this.componentRepository.create({
              ...componentDto,
              category,
            });

            return this.componentRepository.save(component);
          }

          return null;
        }),
      );

      category.components = updatedComponents.filter((comp) => comp !== null);
    }

    const updatedCategory = await this.repository.save(category);

    return updatedCategory;
  }

  async remove(id: number) {
    const category = await this.repository.createQueryBuilder('category')
      .leftJoinAndSelect('category.assetAccountNumber', 'assetAccountNumber')
      .leftJoinAndSelect('category.endowmentAccountNumber', 'endowmentAccountNumber')
      .leftJoinAndSelect('category.amortizationAccountNumber', 'amortizationAccountNumber')
      .leftJoinAndSelect('category.businessUnit', 'businessUnit')
      .leftJoinAndSelect('category.components', 'components')
      .leftJoinAndSelect('category.parent', 'parent')
      .leftJoinAndSelect('category.lexiconEntries', 'lexiconEntries')
      .where('category.id = :id', { id })
      .getOne();

    if (!category) {
      throw new NotFoundException('Category not found');
    }

    return this.repository.remove(category);
  }

  public async deleteMany(ids: number[]): Promise<void> {
    this.repository.delete({
      id: In(ids),
    });
  }

  public save(c: CategoryEntity) {
    return this.repository.save(c);
  }
  
  async getFiltered({
    page,
    limit,
    status,
  }: {
    page: number;
    limit: number;
    status?: 'waiting' | 'validated' | 'unvalidated' | undefined;
  }) {
    const query = this.repository.createQueryBuilder('category')
      .leftJoinAndSelect('category.assetAccountNumber', 'assetAccountNumber')
      .leftJoinAndSelect('category.endowmentAccountNumber', 'endowmentAccountNumber')
      .leftJoinAndSelect('category.amortizationAccountNumber', 'amortizationAccountNumber')
      .leftJoinAndSelect('category.businessUnit', 'businessUnit')
      .leftJoinAndSelect('category.components', 'components')
      .leftJoinAndSelect('category.parent', 'parent')
      .leftJoinAndSelect('category.lexiconEntries', 'lexiconEntries');

    if (status) {
      query.where('category.status = :status', { status });
    }

    query.skip((page - 1) * limit)
      .take(limit);

    const [items, count] = await query.getManyAndCount();

    return {
      count,
      items,
    };
  }

  async multipleChageValidationStatus(
    ids: number[],
    status: 'waiting' | 'validated' | 'unvalidated',
    validatedBy: UserEntity,
  ) {
    return await this.repository.update(
      {
        id: In(ids),
      },
      {
        status,
        validatedBy: status !== 'waiting' ? validatedBy : undefined,
      },
    );
  }

  async getByBarrCodes(codes: string[]) {
    const regions = await this.repository
      .createQueryBuilder('category')
      .leftJoinAndSelect('category.assetAccountNumber', 'assetAccountNumber')
      .leftJoinAndSelect('category.endowmentAccountNumber', 'endowmentAccountNumber')
      .leftJoinAndSelect('category.amortizationAccountNumber', 'amortizationAccountNumber')
      .leftJoinAndSelect('category.businessUnit', 'businessUnit')
      .leftJoinAndSelect('category.components', 'components')
      .where('category.barcode IN (:...codes)', { codes })
      .getMany();

    let map: Record<string, CategoryEntity> = {};

    regions.forEach((r) => {
      if (r.barcode && r.barcode.length > 0) map[r.barcode] = r;
    });
    codes.forEach((c) => {
      if (!map[c]) {
        throw new NotFoundException(`Item with barre code "${c}" not found`);
      }
    });
    return map;
  }}
