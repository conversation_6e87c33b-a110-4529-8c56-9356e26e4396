import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  UseGuards,
  Query,
  HttpException,
  HttpStatus,
  UploadedFile,
  UseInterceptors,
  ConflictException,
} from '@nestjs/common';
import { CategoriesService } from './categories.service';
import {
  CreateCategoryDto,
  DefaultCategoryResponse,
} from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AbilityGuard } from '../auth/guards/abilities.guard';
import { Abilities } from '../auth/decorators/abilities.decorator';
import { ImportExcelDTO } from '../../utils/dto/import.dto';
import { readExcel, cleanExcelData } from '../../utils/files';
import { FileInterceptor } from '@nestjs/platform-express';
import { MulterFile } from '../images/multer.interface';
import { CategoryEntity } from './entities/category.entity';
import { BusinessUnitsService } from '../business-units/business-units.service';
import { extractBareCode } from '../../utils/numbers';
import { UsersService } from '../users/services/users.service';
import { GetCurrentUser } from '../auth/decorators/get-current-user.decorator';
import { QueryFailedError } from 'typeorm';
import { AccountsService } from '../accounts/accounts.service';

@Controller('categories')
@ApiTags('categories')
@ApiBearerAuth('access-token')
@UseGuards(JwtAuthGuard)
export class CategoriesController {
  constructor(
    private readonly service: CategoriesService,
    private readonly userService: UsersService,
    private readonly accountService: AccountsService,
    private readonly businessUnitService: BusinessUnitsService,
  ) {}

  @Post()
  @UseGuards(AbilityGuard)
  @Abilities('EditCategories')
  @ApiResponse({
    status: 201,
    type: DefaultCategoryResponse,
  })
  async create(
    @Body() payload: CreateCategoryDto,
    @GetCurrentUser() user: { id: number },
  ) {
    const data = await this.service.create(payload);
    const u = await this.userService.findOne(user.id);

    data.createdBy = u;
    if (payload.status !== 'waiting') {
      data.validatedBy = u;
    }

    return await this.service.save(data);
  }

  @UseGuards(AbilityGuard)
  @Abilities('ReadBusinessUnits')
  @Get('/barre-code/:id')
  @ApiResponse({
    status: 200,
    type: DefaultCategoryResponse,
  })
  findOneByBarreCode(@Param('id') id: string) {
    return this.service.findOneByBarcode(id);
  }

  @Get('filtered')
  @UseGuards(AbilityGuard)
  @Abilities('ReadCategories')
  @ApiResponse({
    status: 200,
    isArray: true,
    type: DefaultCategoryResponse,
  })
  @ApiQuery({
    name: 'status',
    type: 'string',
    required: false,
    description: 'waiting, validated, unvalidated',
  })
  @ApiQuery({ name: 'page', type: 'string', required: false })
  @ApiQuery({
    name: 'limit',
    type: 'string',
    required: false,
    description: '-1 for all items in one page',
  })
  async findFiltered(
    @Query('page') page: string = '1',
    @Query('limit') limit: string = '100',
    @Query('status') status: 'waiting' | 'validated' | 'unvalidated',
  ) {
    let validStatus: 'waiting' | 'validated' | 'unvalidated' = status;
    if (!['waiting', 'validated', 'unvalidated'].includes(status))
      validStatus = undefined;
    const { count, items } = await this.service.getFiltered({
      page: +page,
      limit: +limit,
      status: validStatus,
    });
    return {
      meta: {
        count,
      },
      data: items,
    };
  }

  @Get()
  @UseGuards(AbilityGuard)
  @Abilities('ReadCategories')
  @ApiResponse({
    status: 200,
    isArray: true,
    type: DefaultCategoryResponse,
  })
  findAll() {
    return this.service.findAll();
  }

  @Patch('validation/multiple')
  @ApiQuery({ name: 'ids', type: 'string' })
  @ApiQuery({
    name: 'status',
    type: 'string',
    enum: ['waiting', 'validated', 'unvalidated'],
  })
  @UseGuards(AbilityGuard)
  @Abilities('EditCategories')
  async validationMultiple(
    @Query('ids') ids: string,
    @GetCurrentUser() user: { id: number },
    @Query('status') status: 'waiting' | 'validated' | 'unvalidated',
  ) {
    const u = await this.userService.findOne(user.id);
    return await this.service.multipleChageValidationStatus(
      ids.split(',').map(Number),
      status,
      u,
    );
  }

  @Get(':id')
  @UseGuards(AbilityGuard)
  @Abilities('ReadCategories')
  @ApiResponse({
    status: 200,
    type: DefaultCategoryResponse,
  })
  findOne(@Param('id') id: string) {
    return this.service.findOne(+id);
  }

  @Patch(':id')
  @UseGuards(AbilityGuard)
  @Abilities('ReadCategories')
  @ApiResponse({
    status: 200,
    type: DefaultCategoryResponse,
  })
  async update(
    @Param('id') id: string,
    @Body() payload: UpdateCategoryDto,
    @GetCurrentUser() user: { id: number },
  ) {
    const data = await this.service.update(+id, payload);
    const u = await this.userService.findOne(user.id);

    if (payload.status !== 'waiting') {
      data.validatedBy = u;
    }
    return await this.service.save(data);
  }

  @Delete(':id')
  @UseGuards(AbilityGuard)
  @Abilities('DeleteCategories')
  remove(@Param('id') id: string) {
    return this.service.remove(+id);
  }

  @Delete()
  @UseGuards(AbilityGuard)
  @Abilities('DeleteCategories')
  @ApiQuery({ name: 'ids', type: 'string' })
  @ApiOperation({ summary: 'Delete multi items by ids ( ?ids=1,2,3,4,5 )' })
  public async deleteMany(@Query('ids') ids: string): Promise<void> {
    if (!ids?.length) {
      return Promise.resolve();
    }
    return this.service.deleteMany(ids?.split(',').map((item) => +item));
  }

  @Post('many/import')
  @UseGuards(JwtAuthGuard, AbilityGuard)
  @Abilities('EditAssetsMovements')
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: ImportExcelDTO,
  })
  @UseInterceptors(FileInterceptor('file'))
  @ApiQuery({ name: 'firstLine', type: 'string' })
  @ApiQuery({ name: 'lastLine', type: 'string' })
  async import(
    @UploadedFile() file: MulterFile,
    @Query('firstLine') firstLine: string = '1',
    @Query('lastLine') lastLine: string,
  ) {
    const excelData = await readExcel(file);

    let fl = !isNaN(+firstLine) ? +firstLine : 1;
    if (fl == 1) fl = 0;
    let ll = !isNaN(+lastLine) ? +lastLine : excelData.length;

    const cleanedData = cleanExcelData<ExelDATA>(excelData.slice(fl, ll + 1));

    const hasProblem = await this.saveData(cleanedData);
    if (hasProblem) {
      throw new HttpException(
        'Some data has not been imported, perhaps due to non-existent codes in the database.',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  private async saveData(data: ExelDATA[]): Promise<boolean> {
    let hasProblem = false;
    const buMap = await this.businessUnitService.getByBarrCodes(
      data.map((d) => extractBareCode(d['CodeBarreBusinessUnit'])),
    );
    const dotationAccNumbers = data.map((d) => d.Dotation);
    const ammortAccNumbers = data.map((d) => d.Amortissement);
    const accountNumbers = [
      ...new Set([...ammortAccNumbers, ...dotationAccNumbers]),
    ];
    const accountsMap = await this.accountService.getByNumbers(accountNumbers);

    for (const e of data) {
      const a = new CategoryEntity();
      a.reference = e.cat_ref;
      a.label = e.cat_name;
      a.businessUnit = buMap[extractBareCode(e.CodeBarreBusinessUnit)];
      a.amortizationAccountNumber =
        accountsMap[extractBareCode(e.Amortissement)];
      a.endowmentAccountNumber = accountsMap[extractBareCode(e.Dotation)];
      a.assetAccountNumber = accountsMap[extractBareCode(e.NroCompte)];

      try {
        await this.service.save(a);
      } catch (error) {
        if (error instanceof QueryFailedError) {
          throw new ConflictException('Duplicated unique fields');
        }
        hasProblem = true;
      }
    }
    return hasProblem;
  }
}

interface ExelDATA {
  cat_ref: string;
  cat_name: string;
  NroCompte: string;
  Dotation: string;
  Amortissement: string;
  CodeBarreBusinessUnit: string;
}
