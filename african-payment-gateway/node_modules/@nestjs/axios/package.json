{"name": "@nestjs/axios", "version": "4.0.1", "description": "Nest - modern, fast, powerful node.js web framework (@axios)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "url": "https://github.com/nestjs/axios#readme", "scripts": {"build": "rimraf -rf dist && tsc -p tsconfig.json", "format": "prettier --write \"{lib,test}/**/*.ts\"", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "test:e2e": "jest --config ./tests/jest-e2e.json --runInBand", "prerelease": "npm run build", "release": "release-it", "prepare": "husky"}, "devDependencies": {"@eslint/eslintrc": "3.3.1", "@eslint/js": "9.31.0", "@commitlint/cli": "19.8.1", "@commitlint/config-angular": "19.8.1", "@nestjs/common": "11.1.3", "@nestjs/core": "11.1.3", "@nestjs/platform-express": "11.1.3", "@nestjs/testing": "11.1.3", "@types/jest": "30.0.0", "@types/node": "22.16.3", "axios": "1.10.0", "eslint": "9.31.0", "eslint-config-prettier": "10.1.5", "eslint-plugin-prettier": "5.5.1", "globals": "16.3.0", "husky": "9.1.7", "jest": "30.0.4", "lint-staged": "16.1.2", "prettier": "3.6.2", "reflect-metadata": "0.2.2", "release-it": "19.0.3", "rimraf": "6.0.1", "rxjs": "7.8.2", "ts-jest": "29.4.0", "typescript": "5.8.3", "typescript-eslint": "8.36.0"}, "peerDependencies": {"@nestjs/common": "^10.0.0 || ^11.0.0", "axios": "^1.3.1", "rxjs": "^7.0.0"}, "lint-staged": {"**/*.{ts,json}": []}, "repository": {"type": "git", "url": "https://github.com/nestjs/axios"}}