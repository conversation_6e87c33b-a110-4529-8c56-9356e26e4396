{"name": "passport-custom", "version": "1.1.1", "description": "Custom authentication strategy for Passport.", "keywords": ["passport", "custom", "auth", "authn", "authentication"], "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git://github.com/mbell8903/passport-custom.git"}, "bugs": {"url": "http://github.com/mbell8903/passport-custom/issues"}, "licenses": [{"type": "MIT", "url": "http://www.opensource.org/licenses/MIT"}], "main": "./lib", "dependencies": {"passport-strategy": "1.x.x"}, "devDependencies": {"chai": "^2.1.0", "chai-passport-strategy": "0.1.x", "mocha": "^2.1.0"}, "engines": {"node": ">= 0.10.0"}, "scripts": {"test": "mocha --reporter spec --require test/bootstrap/node test/*.test.js"}, "homepage": "https://github.com/mbell8903/passport-custom", "directories": {"example": "examples", "test": "test"}, "license": "MIT", "typings": "./lib/index.d.ts"}