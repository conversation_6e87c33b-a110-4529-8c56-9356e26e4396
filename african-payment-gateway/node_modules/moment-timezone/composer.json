{"name": "moment/moment-timezone", "description": "Parse and display dates in any timezone", "version": "0.6.0", "keywords": ["moment", "date", "time", "timezone", "olson", "iana", "zone", "tz"], "homepage": "http://momentjs.com/timezone/", "license": "MIT", "support": {"issues": "https://github.com/moment/moment-timezone/issues", "source": "https://github.com/moment/moment-timezone"}, "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://timwoodcreates.com/"}], "type": "component", "require": {"robloach/component-installer": "*", "moment/moment": ">=2.9.0"}, "extra": {"component": {"scripts": ["moment-timezone.js"], "files": ["builds/*.js"]}}}