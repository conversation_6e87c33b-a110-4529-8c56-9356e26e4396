{"name": "african-payment-gateway", "version": "1.0.0", "description": "A comprehensive, production-ready payment gateway system specifically designed for African e-commerce and digital payments.", "author": "African Payment Gateway Team", "license": "MIT", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "typeorm migration:generate -d src/database/data-source.ts", "migration:run": "typeorm migration:run -d src/database/data-source.ts", "migration:revert": "typeorm migration:revert -d src/database/data-source.ts", "seed": "ts-node src/database/seeds/run-seeds.ts", "docker:build": "docker build -t african-payment-gateway .", "docker:run": "docker run -p 3000:3000 african-payment-gateway"}, "keywords": ["payment-gateway", "africa", "mobile-money", "fintech", "e-commerce", "<PERSON><PERSON><PERSON>", "typescript", "microservices"], "dependencies": {"@nestjs/axios": "^4.0.1", "@nestjs/bull": "^10.0.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.0.0", "@nestjs/throttler": "^5.0.0", "@nestjs/typeorm": "^10.0.0", "axios": "^1.4.0", "bcryptjs": "^2.4.3", "bull": "^4.11.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.8.1", "crypto": "^1.0.1", "express-rate-limit": "^6.8.0", "helmet": "^7.2.0", "joi": "^17.13.3", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.6.0", "nest-winston": "^1.9.4", "passport": "^0.6.0", "passport-custom": "^1.1.1", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.11.0", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/bcryptjs": "^2.4.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.14.195", "@types/node": "^20.3.1", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}