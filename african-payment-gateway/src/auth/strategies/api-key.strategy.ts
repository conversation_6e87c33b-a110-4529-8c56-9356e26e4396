import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-custom';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { <PERSON>pi<PERSON><PERSON> } from '../../database/entities/api-key.entity';
import { Merchant } from '../../database/entities/merchant.entity';
import { EncryptionService } from '../../common/services/encryption.service';

@Injectable()
export class ApiKeyStrategy extends PassportStrategy(Strategy, 'api-key') {
  constructor(
    @InjectRepository(ApiKey)
    private readonly apiKeyRepository: Repository<ApiKey>,
    @InjectRepository(Merchant)
    private readonly merchantRepository: Repository<Merchant>,
    private readonly encryptionService: EncryptionService,
  ) {
    super();
  }

  async validate(request: any): Promise<any> {
    const apiKey = this.extractApiKey(request);
    
    if (!apiKey) {
      throw new UnauthorizedException('API key is required');
    }

    // Hash the provided API key
    const hashedKey = this.encryptionService.hashApiKey(apiKey);

    // Find the API key in database
    const apiKeyRecord = await this.apiKeyRepository.findOne({
      where: { keyHash: hashedKey, status: 'active' },
      relations: ['merchant'],
    });

    if (!apiKeyRecord) {
      throw new UnauthorizedException('Invalid API key');
    }

    // Check if API key is expired
    if (apiKeyRecord.isExpired) {
      throw new UnauthorizedException('API key has expired');
    }

    // Check if merchant is active
    if (!apiKeyRecord.merchant.isActive) {
      throw new UnauthorizedException('Merchant account is not active');
    }

    // Update last used timestamp and usage count
    await this.apiKeyRepository.update(apiKeyRecord.id, {
      lastUsedAt: new Date(),
      usageCount: apiKeyRecord.usageCount + 1,
    });

    // Attach merchant to request
    request.merchant = apiKeyRecord.merchant;
    request.apiKey = apiKeyRecord;

    return {
      merchant: apiKeyRecord.merchant,
      apiKey: apiKeyRecord,
    };
  }

  private extractApiKey(request: any): string | null {
    // Check Authorization header
    const authHeader = request.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Check X-API-Key header
    const apiKeyHeader = request.headers['x-api-key'];
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    // Check query parameter
    const apiKeyQuery = request.query.api_key;
    if (apiKeyQuery) {
      return apiKeyQuery;
    }

    return null;
  }
}
