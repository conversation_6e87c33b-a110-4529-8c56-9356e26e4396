import { Module, Global } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EncryptionService } from './services/encryption.service';
import { LoggingService } from './services/logging.service';
import { ValidationService } from './services/validation.service';
import { UtilsService } from './services/utils.service';
import { AuditService } from './services/audit.service';

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    EncryptionService,
    LoggingService,
    ValidationService,
    UtilsService,
    AuditService,
  ],
  exports: [
    EncryptionService,
    LoggingService,
    ValidationService,
    UtilsService,
    AuditService,
  ],
})
export class CommonModule {}
