import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import * as moment from 'moment-timezone';

@Injectable()
export class UtilsService {
  /**
   * Generate UUID
   */
  generateUuid(): string {
    return uuidv4();
  }

  /**
   * Format currency amount
   */
  formatCurrency(amount: number, currency: string, decimalPlaces: number = 2): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: decimalPlaces,
      maximumFractionDigits: decimalPlaces,
    }).format(amount);
  }

  /**
   * Parse currency amount to number
   */
  parseCurrencyAmount(amount: string | number): number {
    if (typeof amount === 'number') {
      return Math.round(amount * 100) / 100; // Round to 2 decimal places
    }
    
    const parsed = parseFloat(amount.toString().replace(/[^0-9.-]/g, ''));
    return Math.round(parsed * 100) / 100;
  }

  /**
   * Validate email format
   */
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Validate phone number format (international)
   */
  isValidPhoneNumber(phone: string): boolean {
    const phoneRegex = /^\+?[1-9]\d{1,14}$/;
    return phoneRegex.test(phone.replace(/[\s-()]/g, ''));
  }

  /**
   * Format phone number
   */
  formatPhoneNumber(phone: string): string {
    return phone.replace(/[\s-()]/g, '');
  }

  /**
   * Validate African phone number patterns
   */
  validateAfricanPhoneNumber(phone: string, country?: string): boolean {
    const cleanPhone = this.formatPhoneNumber(phone);
    
    const patterns = {
      KE: /^(\+254|254|0)[17]\d{8}$/, // Kenya
      NG: /^(\+234|234|0)[789]\d{9}$/, // Nigeria
      GH: /^(\+233|233|0)[2459]\d{8}$/, // Ghana
      UG: /^(\+256|256|0)[37]\d{8}$/, // Uganda
      TZ: /^(\+255|255|0)[67]\d{8}$/, // Tanzania
      ZA: /^(\+27|27|0)[1-9]\d{8}$/, // South Africa
      RW: /^(\+250|250|0)[78]\d{8}$/, // Rwanda
      ET: /^(\+251|251|0)[9]\d{8}$/, // Ethiopia
    };

    if (country && patterns[country]) {
      return patterns[country].test(cleanPhone);
    }

    // Check against all patterns if no specific country
    return Object.values(patterns).some(pattern => pattern.test(cleanPhone));
  }

  /**
   * Get country code from phone number
   */
  getCountryFromPhoneNumber(phone: string): string | null {
    const cleanPhone = this.formatPhoneNumber(phone);
    
    const countryCodes = {
      '+254': 'KE', '254': 'KE',
      '+234': 'NG', '234': 'NG',
      '+233': 'GH', '233': 'GH',
      '+256': 'UG', '256': 'UG',
      '+255': 'TZ', '255': 'TZ',
      '+27': 'ZA', '27': 'ZA',
      '+250': 'RW', '250': 'RW',
      '+251': 'ET', '251': 'ET',
    };

    for (const [code, country] of Object.entries(countryCodes)) {
      if (cleanPhone.startsWith(code)) {
        return country;
      }
    }

    return null;
  }

  /**
   * Calculate transaction fee
   */
  calculateTransactionFee(
    amount: number,
    feeRate: number,
    minimumFee: number = 0,
    maximumFee?: number,
  ): number {
    let fee = amount * feeRate;
    
    if (fee < minimumFee) {
      fee = minimumFee;
    }
    
    if (maximumFee && fee > maximumFee) {
      fee = maximumFee;
    }
    
    return Math.round(fee * 100) / 100;
  }

  /**
   * Generate reference number
   */
  generateReference(prefix: string = 'REF', length: number = 10): string {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, length - 6).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  }

  /**
   * Format date for different timezones
   */
  formatDate(date: Date, timezone: string = 'UTC', format: string = 'YYYY-MM-DD HH:mm:ss'): string {
    return moment(date).tz(timezone).format(format);
  }

  /**
   * Get business hours for different African countries
   */
  getBusinessHours(country: string): { start: string; end: string; timezone: string } {
    const businessHours = {
      KE: { start: '08:00', end: '17:00', timezone: 'Africa/Nairobi' },
      NG: { start: '08:00', end: '17:00', timezone: 'Africa/Lagos' },
      GH: { start: '08:00', end: '17:00', timezone: 'Africa/Accra' },
      UG: { start: '08:00', end: '17:00', timezone: 'Africa/Kampala' },
      TZ: { start: '08:00', end: '17:00', timezone: 'Africa/Dar_es_Salaam' },
      ZA: { start: '08:00', end: '17:00', timezone: 'Africa/Johannesburg' },
      RW: { start: '08:00', end: '17:00', timezone: 'Africa/Kigali' },
      ET: { start: '08:00', end: '17:00', timezone: 'Africa/Addis_Ababa' },
    };

    return businessHours[country] || businessHours.KE;
  }

  /**
   * Check if current time is within business hours
   */
  isWithinBusinessHours(country: string): boolean {
    const { start, end, timezone } = this.getBusinessHours(country);
    const now = moment().tz(timezone);
    const startTime = moment().tz(timezone).startOf('day').add(moment.duration(start));
    const endTime = moment().tz(timezone).startOf('day').add(moment.duration(end));
    
    return now.isBetween(startTime, endTime);
  }

  /**
   * Sanitize input data
   */
  sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/['"]/g, '') // Remove quotes
      .substring(0, 1000); // Limit length
  }

  /**
   * Deep clone object
   */
  deepClone<T>(obj: T): T {
    return JSON.parse(JSON.stringify(obj));
  }

  /**
   * Check if object is empty
   */
  isEmpty(obj: any): boolean {
    if (obj === null || obj === undefined) return true;
    if (typeof obj === 'string') return obj.length === 0;
    if (Array.isArray(obj)) return obj.length === 0;
    if (typeof obj === 'object') return Object.keys(obj).length === 0;
    return false;
  }

  /**
   * Retry function with exponential backoff
   */
  async retry<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    baseDelay: number = 1000,
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;
        
        if (attempt === maxAttempts) {
          throw lastError;
        }
        
        const delay = baseDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    throw lastError;
  }

  /**
   * Sleep for specified milliseconds
   */
  async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
