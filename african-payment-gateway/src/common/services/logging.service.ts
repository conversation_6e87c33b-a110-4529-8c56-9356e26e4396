import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class LoggingService {
  private readonly logger = new Logger(LoggingService.name);

  constructor(private readonly configService: ConfigService) {}

  /**
   * Log info message
   */
  info(message: string, context?: string): void {
    this.logger.log(message, context);
  }

  /**
   * Log error message
   */
  error(message: string, trace?: string, context?: string): void {
    this.logger.error(message, trace, context);
  }

  /**
   * Log warning message
   */
  warn(message: string, context?: string): void {
    this.logger.warn(message, context);
  }

  /**
   * Log debug message
   */
  debug(message: string, context?: string): void {
    this.logger.debug(message, context);
  }

  /**
   * Log verbose message
   */
  verbose(message: string, context?: string): void {
    this.logger.verbose(message, context);
  }
}
