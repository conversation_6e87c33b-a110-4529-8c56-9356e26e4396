import { Transaction } from '../../database/entities/transaction.entity';

export interface PaymentRequest {
  amount: number;
  currency: string;
  phoneNumber?: string;
  accountNumber?: string;
  accountName?: string;
  bankCode?: string;
  reference: string;
  description?: string;
  callbackUrl?: string;
  metadata?: Record<string, any>;
}

export interface PaymentResponse {
  success: boolean;
  providerReference?: string;
  message: string;
  redirectUrl?: string;
  checkoutRequestId?: string;
  transactionId?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  metadata?: Record<string, any>;
}

export interface PaymentStatusResponse {
  success: boolean;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'expired';
  providerReference?: string;
  amount?: number;
  currency?: string;
  message?: string;
  completedAt?: Date;
  metadata?: Record<string, any>;
}

export interface RefundRequest {
  originalTransactionId: string;
  amount: number;
  currency: string;
  reason?: string;
  reference: string;
}

export interface RefundResponse {
  success: boolean;
  refundId?: string;
  message: string;
  status: 'pending' | 'completed' | 'failed';
  metadata?: Record<string, any>;
}

export interface IPaymentProvider {
  /**
   * Initiate a payment
   */
  initiatePayment(request: PaymentRequest): Promise<PaymentResponse>;

  /**
   * Check payment status
   */
  checkPaymentStatus(providerReference: string): Promise<PaymentStatusResponse>;

  /**
   * Process refund
   */
  processRefund(request: RefundRequest): Promise<RefundResponse>;

  /**
   * Validate webhook signature
   */
  validateWebhookSignature(payload: string, signature: string): boolean;

  /**
   * Process webhook notification
   */
  processWebhookNotification(payload: any): Promise<{
    transactionReference: string;
    status: string;
    providerReference: string;
    amount?: number;
    currency?: string;
    metadata?: Record<string, any>;
  }>;

  /**
   * Get supported currencies
   */
  getSupportedCurrencies(): string[];

  /**
   * Get provider name
   */
  getProviderName(): string;

  /**
   * Validate payment request
   */
  validatePaymentRequest(request: PaymentRequest): Promise<void>;
}
