import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule } from '@nestjs/config';
import { MpesaService } from './services/mpesa.service';
import { AirtelMoneyService } from './services/airtel-money.service';
import { MtnMobileMoneyService } from './services/mtn-mobile-money.service';
import { BankTransferService } from './services/bank-transfer.service';
import { CardPaymentService } from './services/card-payment.service';
import { PaymentProviderFactory } from './factories/payment-provider.factory';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    HttpModule.register({
      timeout: 30000,
      maxRedirects: 5,
    }),
    ConfigModule,
    CommonModule,
  ],
  providers: [
    MpesaService,
    AirtelMoneyService,
    MtnMobileMoneyService,
    BankTransferService,
    CardPaymentService,
    PaymentProviderFactory,
  ],
  exports: [
    MpesaService,
    AirtelMoneyService,
    MtnMobileMoneyService,
    BankTransferService,
    CardPaymentService,
    PaymentProviderFactory,
  ],
})
export class IntegrationsModule {}
