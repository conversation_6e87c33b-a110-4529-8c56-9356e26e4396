import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { 
  IPaymentProvider, 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatusResponse,
  RefundRequest,
  RefundResponse 
} from '../interfaces/payment-provider.interface';
import { EncryptionService } from '../../common/services/encryption.service';
import { UtilsService } from '../../common/services/utils.service';

@Injectable()
export class AirtelMoneyService implements IPaymentProvider {
  private readonly logger = new Logger(AirtelMoneyService.name);
  private readonly baseUrl: string;
  private readonly clientId: string;
  private readonly clientSecret: string;
  private readonly environment: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly encryptionService: EncryptionService,
    private readonly utilsService: UtilsService,
  ) {
    this.environment = this.configService.get<string>('AIRTEL_ENVIRONMENT', 'sandbox');
    this.baseUrl = this.environment === 'production' 
      ? 'https://openapiuat.airtel.africa' 
      : 'https://openapiuat.airtel.africa';
    this.clientId = this.configService.get<string>('AIRTEL_CLIENT_ID');
    this.clientSecret = this.configService.get<string>('AIRTEL_CLIENT_SECRET');
  }

  async initiatePayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      this.logger.log(`Initiating Airtel Money payment: ${request.reference}`);

      await this.validatePaymentRequest(request);

      // Get access token
      const accessToken = await this.getAccessToken();

      // Determine country code from phone number
      const countryCode = this.getCountryCodeFromPhone(request.phoneNumber);

      // Prepare payment request
      const paymentPayload = {
        reference: request.reference,
        subscriber: {
          country: countryCode,
          currency: request.currency,
          msisdn: this.formatPhoneNumber(request.phoneNumber),
        },
        transaction: {
          amount: request.amount,
          country: countryCode,
          currency: request.currency,
          id: request.reference,
        },
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/merchant/v1/payments/`,
          paymentPayload,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
              'X-Country': countryCode,
              'X-Currency': request.currency,
            },
          },
        ),
      );

      if (response.data.status?.success) {
        return {
          success: true,
          providerReference: response.data.data?.transaction?.id,
          transactionId: response.data.data?.transaction?.id,
          message: 'Payment initiated successfully',
          status: 'pending',
          metadata: response.data,
        };
      } else {
        throw new Error(`Airtel Money payment failed: ${response.data.status?.message}`);
      }
    } catch (error) {
      this.logger.error(`Airtel Money payment initiation failed: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message,
        status: 'failed',
      };
    }
  }

  async checkPaymentStatus(transactionId: string): Promise<PaymentStatusResponse> {
    try {
      this.logger.log(`Checking Airtel Money payment status: ${transactionId}`);

      const accessToken = await this.getAccessToken();

      const response = await firstValueFrom(
        this.httpService.get(
          `${this.baseUrl}/standard/v1/payments/${transactionId}`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      const transaction = response.data.data?.transaction;
      let status: PaymentStatusResponse['status'];

      switch (transaction?.status) {
        case 'TS':
          status = 'completed';
          break;
        case 'TF':
          status = 'failed';
          break;
        case 'TA':
          status = 'pending';
          break;
        default:
          status = 'pending';
      }

      return {
        success: true,
        status,
        providerReference: transactionId,
        amount: transaction?.amount,
        currency: transaction?.currency,
        message: response.data.status?.message,
        metadata: response.data,
      };
    } catch (error) {
      this.logger.error(`Airtel Money status check failed: ${error.message}`, error.stack);
      return {
        success: false,
        status: 'failed',
        message: error.message,
      };
    }
  }

  async processRefund(request: RefundRequest): Promise<RefundResponse> {
    try {
      this.logger.log(`Processing Airtel Money refund: ${request.reference}`);

      const accessToken = await this.getAccessToken();

      const refundPayload = {
        transaction: {
          airtel_money_id: request.originalTransactionId,
        },
        reference: request.reference,
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/standard/v1/payments/refund`,
          refundPayload,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      if (response.data.status?.success) {
        return {
          success: true,
          refundId: response.data.data?.transaction?.id,
          message: response.data.status?.message,
          status: 'pending',
          metadata: response.data,
        };
      } else {
        throw new Error(`Airtel Money refund failed: ${response.data.status?.message}`);
      }
    } catch (error) {
      this.logger.error(`Airtel Money refund failed: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message,
        status: 'failed',
      };
    }
  }

  validateWebhookSignature(payload: string, signature: string): boolean {
    // Implement Airtel Money webhook signature validation
    // This would use their specific signature algorithm
    return true;
  }

  async processWebhookNotification(payload: any): Promise<{
    transactionReference: string;
    status: string;
    providerReference: string;
    amount?: number;
    currency?: string;
    metadata?: Record<string, any>;
  }> {
    const transaction = payload.transaction;
    
    if (!transaction) {
      throw new Error('Invalid Airtel Money webhook payload');
    }

    let status: string;
    switch (transaction.status) {
      case 'TS':
        status = 'completed';
        break;
      case 'TF':
        status = 'failed';
        break;
      case 'TA':
        status = 'pending';
        break;
      default:
        status = 'failed';
    }

    return {
      transactionReference: transaction.reference,
      status,
      providerReference: transaction.id,
      amount: transaction.amount,
      currency: transaction.currency,
      metadata: payload,
    };
  }

  getSupportedCurrencies(): string[] {
    return ['KES', 'UGX', 'TZS', 'RWF', 'USD'];
  }

  getProviderName(): string {
    return 'Airtel Money';
  }

  async validatePaymentRequest(request: PaymentRequest): Promise<void> {
    if (!request.phoneNumber) {
      throw new BadRequestException('Phone number is required for Airtel Money payments');
    }

    const supportedCurrencies = this.getSupportedCurrencies();
    if (!supportedCurrencies.includes(request.currency)) {
      throw new BadRequestException(`Currency ${request.currency} is not supported by Airtel Money`);
    }

    // Validate phone number format for supported countries
    const countryCode = this.getCountryCodeFromPhone(request.phoneNumber);
    if (!countryCode) {
      throw new BadRequestException('Invalid phone number for Airtel Money');
    }

    // Currency-specific amount validation
    const limits = this.getAmountLimits(request.currency);
    if (request.amount < limits.min || request.amount > limits.max) {
      throw new BadRequestException(
        `Amount must be between ${limits.min} and ${limits.max} ${request.currency}`,
      );
    }
  }

  private async getAccessToken(): Promise<string> {
    try {
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/auth/oauth2/token`,
          {
            client_id: this.clientId,
            client_secret: this.clientSecret,
            grant_type: 'client_credentials',
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      return response.data.access_token;
    } catch (error) {
      this.logger.error('Failed to get Airtel Money access token', error.stack);
      throw new Error('Failed to authenticate with Airtel Money');
    }
  }

  private formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-numeric characters
    let formatted = phoneNumber.replace(/\D/g, '');
    
    // Remove leading zeros or country codes
    if (formatted.startsWith('0')) {
      formatted = formatted.substring(1);
    } else if (formatted.startsWith('254')) {
      formatted = formatted.substring(3);
    } else if (formatted.startsWith('256')) {
      formatted = formatted.substring(3);
    } else if (formatted.startsWith('255')) {
      formatted = formatted.substring(3);
    } else if (formatted.startsWith('250')) {
      formatted = formatted.substring(3);
    }

    return formatted;
  }

  private getCountryCodeFromPhone(phoneNumber: string): string | null {
    const cleanPhone = phoneNumber.replace(/\D/g, '');
    
    if (cleanPhone.startsWith('254') || cleanPhone.startsWith('0')) {
      return 'KE';
    } else if (cleanPhone.startsWith('256')) {
      return 'UG';
    } else if (cleanPhone.startsWith('255')) {
      return 'TZ';
    } else if (cleanPhone.startsWith('250')) {
      return 'RW';
    }

    return null;
  }

  private getAmountLimits(currency: string): { min: number; max: number } {
    const limits = {
      KES: { min: 10, max: 150000 },
      UGX: { min: 1000, max: 5000000 },
      TZS: { min: 1000, max: 3000000 },
      RWF: { min: 100, max: 1000000 },
      USD: { min: 1, max: 1000 },
    };

    return limits[currency] || { min: 1, max: 1000000 };
  }
}
