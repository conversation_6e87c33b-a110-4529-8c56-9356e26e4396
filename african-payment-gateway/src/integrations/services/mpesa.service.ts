import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { 
  IPaymentProvider, 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatusResponse,
  RefundRequest,
  RefundResponse 
} from '../interfaces/payment-provider.interface';
import { EncryptionService } from '../../common/services/encryption.service';
import { UtilsService } from '../../common/services/utils.service';

@Injectable()
export class MpesaService implements IPaymentProvider {
  private readonly logger = new Logger(MpesaService.name);
  private readonly baseUrl: string;
  private readonly consumerKey: string;
  private readonly consumerSecret: string;
  private readonly shortcode: string;
  private readonly passkey: string;
  private readonly environment: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly encryptionService: EncryptionService,
    private readonly utilsService: UtilsService,
  ) {
    this.environment = this.configService.get<string>('MPESA_ENVIRONMENT', 'sandbox');
    this.baseUrl = this.environment === 'production' 
      ? 'https://api.safaricom.co.ke' 
      : 'https://sandbox.safaricom.co.ke';
    this.consumerKey = this.configService.get<string>('MPESA_CONSUMER_KEY');
    this.consumerSecret = this.configService.get<string>('MPESA_CONSUMER_SECRET');
    this.shortcode = this.configService.get<string>('MPESA_SHORTCODE');
    this.passkey = this.configService.get<string>('MPESA_PASSKEY');
  }

  async initiatePayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      this.logger.log(`Initiating M-Pesa payment: ${request.reference}`);

      await this.validatePaymentRequest(request);

      // Get access token
      const accessToken = await this.getAccessToken();

      // Generate timestamp and password
      const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3);
      const password = Buffer.from(`${this.shortcode}${this.passkey}${timestamp}`).toString('base64');

      // Prepare STK Push request
      const stkPushPayload = {
        BusinessShortCode: this.shortcode,
        Password: password,
        Timestamp: timestamp,
        TransactionType: 'CustomerPayBillOnline',
        Amount: Math.round(request.amount),
        PartyA: this.formatPhoneNumber(request.phoneNumber),
        PartyB: this.shortcode,
        PhoneNumber: this.formatPhoneNumber(request.phoneNumber),
        CallBackURL: request.callbackUrl,
        AccountReference: request.reference,
        TransactionDesc: request.description || 'Payment',
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/mpesa/stkpush/v1/processrequest`,
          stkPushPayload,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      if (response.data.ResponseCode === '0') {
        return {
          success: true,
          providerReference: response.data.CheckoutRequestID,
          checkoutRequestId: response.data.CheckoutRequestID,
          message: response.data.ResponseDescription,
          status: 'pending',
          metadata: {
            merchantRequestId: response.data.MerchantRequestID,
            checkoutRequestId: response.data.CheckoutRequestID,
          },
        };
      } else {
        throw new Error(`M-Pesa STK Push failed: ${response.data.ResponseDescription}`);
      }
    } catch (error) {
      this.logger.error(`M-Pesa payment initiation failed: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message,
        status: 'failed',
      };
    }
  }

  async checkPaymentStatus(checkoutRequestId: string): Promise<PaymentStatusResponse> {
    try {
      this.logger.log(`Checking M-Pesa payment status: ${checkoutRequestId}`);

      const accessToken = await this.getAccessToken();
      const timestamp = new Date().toISOString().replace(/[^0-9]/g, '').slice(0, -3);
      const password = Buffer.from(`${this.shortcode}${this.passkey}${timestamp}`).toString('base64');

      const queryPayload = {
        BusinessShortCode: this.shortcode,
        Password: password,
        Timestamp: timestamp,
        CheckoutRequestID: checkoutRequestId,
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/mpesa/stkpushquery/v1/query`,
          queryPayload,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      const resultCode = response.data.ResultCode;
      let status: PaymentStatusResponse['status'];

      switch (resultCode) {
        case '0':
          status = 'completed';
          break;
        case '1032':
          status = 'cancelled';
          break;
        case '1037':
          status = 'expired';
          break;
        default:
          status = resultCode ? 'failed' : 'pending';
      }

      return {
        success: true,
        status,
        providerReference: checkoutRequestId,
        message: response.data.ResultDesc,
        metadata: response.data,
      };
    } catch (error) {
      this.logger.error(`M-Pesa status check failed: ${error.message}`, error.stack);
      return {
        success: false,
        status: 'failed',
        message: error.message,
      };
    }
  }

  async processRefund(request: RefundRequest): Promise<RefundResponse> {
    try {
      this.logger.log(`Processing M-Pesa refund: ${request.reference}`);

      const accessToken = await this.getAccessToken();

      const refundPayload = {
        Initiator: 'testapi',
        SecurityCredential: await this.generateSecurityCredential(),
        CommandID: 'TransactionReversal',
        TransactionID: request.originalTransactionId,
        Amount: Math.round(request.amount),
        ReceiverParty: this.shortcode,
        RecieverIdentifierType: '11',
        ResultURL: `${this.configService.get('APP_URL')}/webhooks/mpesa/refund`,
        QueueTimeOutURL: `${this.configService.get('APP_URL')}/webhooks/mpesa/timeout`,
        Remarks: request.reason || 'Refund',
        Occasion: request.reference,
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/mpesa/reversal/v1/request`,
          refundPayload,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
            },
          },
        ),
      );

      if (response.data.ResponseCode === '0') {
        return {
          success: true,
          refundId: response.data.ConversationID,
          message: response.data.ResponseDescription,
          status: 'pending',
          metadata: response.data,
        };
      } else {
        throw new Error(`M-Pesa refund failed: ${response.data.ResponseDescription}`);
      }
    } catch (error) {
      this.logger.error(`M-Pesa refund failed: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message,
        status: 'failed',
      };
    }
  }

  validateWebhookSignature(payload: string, signature: string): boolean {
    // M-Pesa doesn't use signature validation in the same way
    // Instead, we validate the source IP and other security measures
    return true;
  }

  async processWebhookNotification(payload: any): Promise<{
    transactionReference: string;
    status: string;
    providerReference: string;
    amount?: number;
    currency?: string;
    metadata?: Record<string, any>;
  }> {
    const body = payload.Body?.stkCallback;
    
    if (!body) {
      throw new Error('Invalid M-Pesa webhook payload');
    }

    const resultCode = body.ResultCode;
    let status: string;

    switch (resultCode) {
      case 0:
        status = 'completed';
        break;
      case 1032:
        status = 'cancelled';
        break;
      case 1037:
        status = 'expired';
        break;
      default:
        status = 'failed';
    }

    const callbackMetadata = body.CallbackMetadata?.Item || [];
    const amount = callbackMetadata.find(item => item.Name === 'Amount')?.Value;
    const mpesaReceiptNumber = callbackMetadata.find(item => item.Name === 'MpesaReceiptNumber')?.Value;
    const phoneNumber = callbackMetadata.find(item => item.Name === 'PhoneNumber')?.Value;

    return {
      transactionReference: body.CheckoutRequestID,
      status,
      providerReference: mpesaReceiptNumber || body.CheckoutRequestID,
      amount,
      currency: 'KES',
      metadata: {
        resultCode,
        resultDesc: body.ResultDesc,
        phoneNumber,
        mpesaReceiptNumber,
        callbackMetadata,
      },
    };
  }

  getSupportedCurrencies(): string[] {
    return ['KES'];
  }

  getProviderName(): string {
    return 'M-Pesa';
  }

  async validatePaymentRequest(request: PaymentRequest): Promise<void> {
    if (!request.phoneNumber) {
      throw new BadRequestException('Phone number is required for M-Pesa payments');
    }

    if (!this.utilsService.validateAfricanPhoneNumber(request.phoneNumber, 'KE')) {
      throw new BadRequestException('Invalid Kenyan phone number for M-Pesa');
    }

    if (request.currency !== 'KES') {
      throw new BadRequestException('M-Pesa only supports KES currency');
    }

    if (request.amount < 1 || request.amount > 150000) {
      throw new BadRequestException('M-Pesa amount must be between 1 and 150,000 KES');
    }
  }

  private async getAccessToken(): Promise<string> {
    try {
      const auth = Buffer.from(`${this.consumerKey}:${this.consumerSecret}`).toString('base64');

      const response = await firstValueFrom(
        this.httpService.get(
          `${this.baseUrl}/oauth/v1/generate?grant_type=client_credentials`,
          {
            headers: {
              'Authorization': `Basic ${auth}`,
            },
          },
        ),
      );

      return response.data.access_token;
    } catch (error) {
      this.logger.error('Failed to get M-Pesa access token', error.stack);
      throw new Error('Failed to authenticate with M-Pesa');
    }
  }

  private formatPhoneNumber(phoneNumber: string): string {
    // Convert to 254XXXXXXXXX format
    let formatted = phoneNumber.replace(/[\s-()]/g, '');
    
    if (formatted.startsWith('0')) {
      formatted = '254' + formatted.substring(1);
    } else if (formatted.startsWith('+254')) {
      formatted = formatted.substring(1);
    } else if (!formatted.startsWith('254')) {
      formatted = '254' + formatted;
    }

    return formatted;
  }

  private async generateSecurityCredential(): Promise<string> {
    // In production, this would use the actual M-Pesa public key
    // For now, return a placeholder
    return 'placeholder_security_credential';
  }
}
