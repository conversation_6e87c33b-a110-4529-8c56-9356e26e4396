import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';
import { 
  IPaymentProvider, 
  PaymentRequest, 
  PaymentResponse, 
  PaymentStatusResponse,
  RefundRequest,
  RefundResponse 
} from '../interfaces/payment-provider.interface';
import { EncryptionService } from '../../common/services/encryption.service';
import { UtilsService } from '../../common/services/utils.service';

@Injectable()
export class MtnMobileMoneyService implements IPaymentProvider {
  private readonly logger = new Logger(MtnMobileMoneyService.name);
  private readonly baseUrl: string;
  private readonly subscriptionKey: string;
  private readonly apiUser: string;
  private readonly apiKey: string;
  private readonly environment: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    private readonly encryptionService: EncryptionService,
    private readonly utilsService: UtilsService,
  ) {
    this.environment = this.configService.get<string>('MTN_ENVIRONMENT', 'sandbox');
    this.baseUrl = this.environment === 'production' 
      ? 'https://ericssonbasicapi2.azure-api.net' 
      : 'https://sandbox.momodeveloper.mtn.com';
    this.subscriptionKey = this.configService.get<string>('MTN_SUBSCRIPTION_KEY');
    this.apiUser = this.configService.get<string>('MTN_API_USER');
    this.apiKey = this.configService.get<string>('MTN_API_KEY');
  }

  async initiatePayment(request: PaymentRequest): Promise<PaymentResponse> {
    try {
      this.logger.log(`Initiating MTN Mobile Money payment: ${request.reference}`);

      await this.validatePaymentRequest(request);

      // Get access token
      const accessToken = await this.getAccessToken();

      // Generate unique reference ID
      const referenceId = this.utilsService.generateUuid();

      // Prepare payment request
      const paymentPayload = {
        amount: request.amount.toString(),
        currency: request.currency,
        externalId: request.reference,
        payer: {
          partyIdType: 'MSISDN',
          partyId: this.formatPhoneNumber(request.phoneNumber),
        },
        payerMessage: request.description || 'Payment',
        payeeNote: `Payment for ${request.reference}`,
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/collection/v1_0/requesttopay`,
          paymentPayload,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
              'Ocp-Apim-Subscription-Key': this.subscriptionKey,
              'X-Reference-Id': referenceId,
              'X-Target-Environment': this.environment,
            },
          },
        ),
      );

      // MTN Mobile Money returns 202 for successful initiation
      if (response.status === 202) {
        return {
          success: true,
          providerReference: referenceId,
          transactionId: referenceId,
          message: 'Payment initiated successfully',
          status: 'pending',
          metadata: {
            referenceId,
            externalId: request.reference,
          },
        };
      } else {
        throw new Error('MTN Mobile Money payment initiation failed');
      }
    } catch (error) {
      this.logger.error(`MTN Mobile Money payment initiation failed: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message,
        status: 'failed',
      };
    }
  }

  async checkPaymentStatus(referenceId: string): Promise<PaymentStatusResponse> {
    try {
      this.logger.log(`Checking MTN Mobile Money payment status: ${referenceId}`);

      const accessToken = await this.getAccessToken();

      const response = await firstValueFrom(
        this.httpService.get(
          `${this.baseUrl}/collection/v1_0/requesttopay/${referenceId}`,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Ocp-Apim-Subscription-Key': this.subscriptionKey,
              'X-Target-Environment': this.environment,
            },
          },
        ),
      );

      const transaction = response.data;
      let status: PaymentStatusResponse['status'];

      switch (transaction.status) {
        case 'SUCCESSFUL':
          status = 'completed';
          break;
        case 'FAILED':
          status = 'failed';
          break;
        case 'PENDING':
          status = 'pending';
          break;
        case 'REJECTED':
          status = 'cancelled';
          break;
        default:
          status = 'pending';
      }

      return {
        success: true,
        status,
        providerReference: referenceId,
        amount: parseFloat(transaction.amount),
        currency: transaction.currency,
        message: transaction.reason?.message || 'Status retrieved successfully',
        metadata: transaction,
      };
    } catch (error) {
      this.logger.error(`MTN Mobile Money status check failed: ${error.message}`, error.stack);
      return {
        success: false,
        status: 'failed',
        message: error.message,
      };
    }
  }

  async processRefund(request: RefundRequest): Promise<RefundResponse> {
    try {
      this.logger.log(`Processing MTN Mobile Money refund: ${request.reference}`);

      const accessToken = await this.getAccessToken();
      const referenceId = this.utilsService.generateUuid();

      const refundPayload = {
        amount: request.amount.toString(),
        currency: request.currency,
        externalId: request.reference,
        payee: {
          partyIdType: 'MSISDN',
          partyId: '************', // This would be the original payer's number
        },
        payerMessage: request.reason || 'Refund',
        payeeNote: `Refund for ${request.originalTransactionId}`,
      };

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/disbursement/v1_0/transfer`,
          refundPayload,
          {
            headers: {
              'Authorization': `Bearer ${accessToken}`,
              'Content-Type': 'application/json',
              'Ocp-Apim-Subscription-Key': this.subscriptionKey,
              'X-Reference-Id': referenceId,
              'X-Target-Environment': this.environment,
            },
          },
        ),
      );

      if (response.status === 202) {
        return {
          success: true,
          refundId: referenceId,
          message: 'Refund initiated successfully',
          status: 'pending',
          metadata: {
            referenceId,
            externalId: request.reference,
          },
        };
      } else {
        throw new Error('MTN Mobile Money refund initiation failed');
      }
    } catch (error) {
      this.logger.error(`MTN Mobile Money refund failed: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message,
        status: 'failed',
      };
    }
  }

  validateWebhookSignature(payload: string, signature: string): boolean {
    // Implement MTN Mobile Money webhook signature validation
    // This would use their specific signature algorithm
    return true;
  }

  async processWebhookNotification(payload: any): Promise<{
    transactionReference: string;
    status: string;
    providerReference: string;
    amount?: number;
    currency?: string;
    metadata?: Record<string, any>;
  }> {
    // MTN Mobile Money webhook structure
    const transaction = payload;
    
    if (!transaction) {
      throw new Error('Invalid MTN Mobile Money webhook payload');
    }

    let status: string;
    switch (transaction.status) {
      case 'SUCCESSFUL':
        status = 'completed';
        break;
      case 'FAILED':
        status = 'failed';
        break;
      case 'PENDING':
        status = 'pending';
        break;
      case 'REJECTED':
        status = 'cancelled';
        break;
      default:
        status = 'failed';
    }

    return {
      transactionReference: transaction.externalId,
      status,
      providerReference: transaction.financialTransactionId || transaction.referenceId,
      amount: parseFloat(transaction.amount),
      currency: transaction.currency,
      metadata: payload,
    };
  }

  getSupportedCurrencies(): string[] {
    return ['GHS', 'UGX', 'RWF', 'EUR'];
  }

  getProviderName(): string {
    return 'MTN Mobile Money';
  }

  async validatePaymentRequest(request: PaymentRequest): Promise<void> {
    if (!request.phoneNumber) {
      throw new BadRequestException('Phone number is required for MTN Mobile Money payments');
    }

    const supportedCurrencies = this.getSupportedCurrencies();
    if (!supportedCurrencies.includes(request.currency)) {
      throw new BadRequestException(`Currency ${request.currency} is not supported by MTN Mobile Money`);
    }

    // Validate phone number format for supported countries
    const countryCode = this.getCountryCodeFromPhone(request.phoneNumber);
    if (!countryCode) {
      throw new BadRequestException('Invalid phone number for MTN Mobile Money');
    }

    // Currency-specific amount validation
    const limits = this.getAmountLimits(request.currency);
    if (request.amount < limits.min || request.amount > limits.max) {
      throw new BadRequestException(
        `Amount must be between ${limits.min} and ${limits.max} ${request.currency}`,
      );
    }
  }

  private async getAccessToken(): Promise<string> {
    try {
      const auth = Buffer.from(`${this.apiUser}:${this.apiKey}`).toString('base64');

      const response = await firstValueFrom(
        this.httpService.post(
          `${this.baseUrl}/collection/token/`,
          {},
          {
            headers: {
              'Authorization': `Basic ${auth}`,
              'Ocp-Apim-Subscription-Key': this.subscriptionKey,
              'X-Target-Environment': this.environment,
            },
          },
        ),
      );

      return response.data.access_token;
    } catch (error) {
      this.logger.error('Failed to get MTN Mobile Money access token', error.stack);
      throw new Error('Failed to authenticate with MTN Mobile Money');
    }
  }

  private formatPhoneNumber(phoneNumber: string): string {
    // Remove all non-numeric characters
    let formatted = phoneNumber.replace(/\D/g, '');
    
    // Add country code if not present
    if (formatted.startsWith('0')) {
      // Remove leading zero and add appropriate country code
      formatted = formatted.substring(1);
      // Default to Ghana if no specific country code logic
      formatted = '233' + formatted;
    } else if (!formatted.startsWith('233') && !formatted.startsWith('256') && !formatted.startsWith('250')) {
      // Add default country code (Ghana)
      formatted = '233' + formatted;
    }

    return formatted;
  }

  private getCountryCodeFromPhone(phoneNumber: string): string | null {
    const cleanPhone = phoneNumber.replace(/\D/g, '');
    
    if (cleanPhone.startsWith('233')) {
      return 'GH';
    } else if (cleanPhone.startsWith('256')) {
      return 'UG';
    } else if (cleanPhone.startsWith('250')) {
      return 'RW';
    }

    return null;
  }

  private getAmountLimits(currency: string): { min: number; max: number } {
    const limits = {
      GHS: { min: 1, max: 50000 },
      UGX: { min: 1000, max: 5000000 },
      RWF: { min: 100, max: 1000000 },
      EUR: { min: 1, max: 1000 },
    };

    return limits[currency] || { min: 1, max: 1000000 };
  }
}
