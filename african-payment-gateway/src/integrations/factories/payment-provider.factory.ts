import { Injectable, BadRequestException } from '@nestjs/common';
import { PaymentMethod } from '../../database/entities/transaction.entity';
import { IPaymentProvider } from '../interfaces/payment-provider.interface';
import { MpesaService } from '../services/mpesa.service';
import { AirtelMoneyService } from '../services/airtel-money.service';
import { MtnMobileMoneyService } from '../services/mtn-mobile-money.service';
import { BankTransferService } from '../services/bank-transfer.service';
import { CardPaymentService } from '../services/card-payment.service';

@Injectable()
export class PaymentProviderFactory {
  constructor(
    private readonly mpesaService: MpesaService,
    private readonly airtelMoneyService: AirtelMoneyService,
    private readonly mtnMobileMoneyService: MtnMobileMoneyService,
    private readonly bankTransferService: BankTransferService,
    private readonly cardPaymentService: CardPaymentService,
  ) {}

  /**
   * Get payment provider based on payment method
   */
  getProvider(paymentMethod: PaymentMethod): IPaymentProvider {
    switch (paymentMethod) {
      case PaymentMethod.MPESA:
        return this.mpesaService;
      case PaymentMethod.AIRTEL_MONEY:
        return this.airtelMoneyService;
      case PaymentMethod.MTN_MOBILE_MONEY:
        return this.mtnMobileMoneyService;
      case PaymentMethod.BANK_TRANSFER:
        return this.bankTransferService;
      case PaymentMethod.CARD:
        return this.cardPaymentService;
      default:
        throw new BadRequestException(`Unsupported payment method: ${paymentMethod}`);
    }
  }

  /**
   * Get all available providers
   */
  getAllProviders(): IPaymentProvider[] {
    return [
      this.mpesaService,
      this.airtelMoneyService,
      this.mtnMobileMoneyService,
      this.bankTransferService,
      this.cardPaymentService,
    ];
  }

  /**
   * Get providers that support a specific currency
   */
  getProvidersByCurrency(currency: string): IPaymentProvider[] {
    return this.getAllProviders().filter(provider =>
      provider.getSupportedCurrencies().includes(currency),
    );
  }

  /**
   * Check if payment method is supported for currency
   */
  isPaymentMethodSupportedForCurrency(
    paymentMethod: PaymentMethod,
    currency: string,
  ): boolean {
    try {
      const provider = this.getProvider(paymentMethod);
      return provider.getSupportedCurrencies().includes(currency);
    } catch {
      return false;
    }
  }

  /**
   * Get supported payment methods for currency
   */
  getSupportedPaymentMethodsForCurrency(currency: string): PaymentMethod[] {
    const supportedMethods: PaymentMethod[] = [];

    Object.values(PaymentMethod).forEach(method => {
      if (this.isPaymentMethodSupportedForCurrency(method, currency)) {
        supportedMethods.push(method);
      }
    });

    return supportedMethods;
  }
}
