import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Currency } from '../database/entities/currency.entity';
import { ExchangeRateService } from './services/exchange-rate.service';

@Injectable()
export class CurrenciesService {
  constructor(
    @InjectRepository(Currency)
    private readonly currencyRepository: Repository<Currency>,
    private readonly exchangeRateService: ExchangeRateService,
  ) {}

  async findAll() {
    return this.currencyRepository.find({
      where: { isActive: true },
      order: { code: 'ASC' }
    });
  }

  async findByCode(code: string) {
    return this.currencyRepository.findOne({ where: { code, isActive: true } });
  }

  async getExchangeRate(fromCurrency: string, toCurrency: string) {
    return this.exchangeRateService.getExchangeRate(fromCurrency, toCurrency);
  }

  async convertAmount(amount: number, fromCurrency: string, toCurrency: string) {
    return this.exchangeRateService.convertAmount(amount, fromCurrency, toCurrency);
  }

  async getSupportedCurrencies() {
    return this.exchangeRateService.getSupportedCurrenciesWithRates();
  }

  async updateExchangeRates() {
    return this.exchangeRateService.updateAllExchangeRates();
  }

  async getBaseCurrency() {
    return this.exchangeRateService.getBaseCurrency();
  }

  async getCurrencyInfo(code: string) {
    const currency = await this.findByCode(code);
    if (!currency) {
      throw new Error(`Currency ${code} not found`);
    }

    return {
      code: currency.code,
      name: currency.name,
      symbol: currency.symbol,
      decimalPlaces: currency.decimalPlaces,
      exchangeRate: currency.exchangeRate,
      lastUpdated: currency.lastUpdated,
      country: currency.country,
      metadata: currency.metadata,
    };
  }

  async formatAmount(amount: number, currencyCode: string): Promise<string> {
    const currency = await this.findByCode(currencyCode);
    if (!currency) {
      return `${amount} ${currencyCode}`;
    }

    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.code,
      minimumFractionDigits: currency.decimalPlaces,
      maximumFractionDigits: currency.decimalPlaces,
    }).format(amount);
  }
}
