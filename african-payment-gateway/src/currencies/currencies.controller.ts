import { Controller, Get, Query, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { CurrenciesService } from './currencies.service';
import { ApiKeyGuard } from '../auth/guards/api-key.guard';

@ApiTags('currencies')
@Controller('currencies')
@UseGuards(ApiKeyGuard)
export class CurrenciesController {
  constructor(private readonly currenciesService: CurrenciesService) {}

  @Get()
  @ApiOperation({ summary: 'Get all supported currencies' })
  @ApiResponse({ status: 200, description: 'List of supported currencies' })
  async getSupportedCurrencies() {
    const currencies = await this.currenciesService.findAll();
    
    return {
      success: true,
      data: currencies.map(currency => ({
        code: currency.code,
        name: currency.name,
        symbol: currency.symbol,
        decimalPlaces: currency.decimalPlaces,
        country: currency.country,
        exchangeRate: currency.exchangeRate,
        lastUpdated: currency.lastUpdated,
      })),
    };
  }

  @Get(':code')
  @ApiOperation({ summary: 'Get currency information by code' })
  @ApiResponse({ status: 200, description: 'Currency information' })
  @ApiResponse({ status: 404, description: 'Currency not found' })
  async getCurrencyInfo(@Param('code') code: string) {
    const currencyInfo = await this.currenciesService.getCurrencyInfo(code.toUpperCase());
    
    return {
      success: true,
      data: currencyInfo,
    };
  }

  @Get('exchange-rate/:from/:to')
  @ApiOperation({ summary: 'Get exchange rate between two currencies' })
  @ApiResponse({ status: 200, description: 'Exchange rate information' })
  async getExchangeRate(
    @Param('from') fromCurrency: string,
    @Param('to') toCurrency: string,
  ) {
    const rate = await this.currenciesService.getExchangeRate(
      fromCurrency.toUpperCase(),
      toCurrency.toUpperCase(),
    );
    
    return {
      success: true,
      data: {
        from: fromCurrency.toUpperCase(),
        to: toCurrency.toUpperCase(),
        rate,
        timestamp: new Date(),
      },
    };
  }

  @Get('convert/:amount/:from/:to')
  @ApiOperation({ summary: 'Convert amount between currencies' })
  @ApiResponse({ status: 200, description: 'Converted amount' })
  async convertAmount(
    @Param('amount') amount: number,
    @Param('from') fromCurrency: string,
    @Param('to') toCurrency: string,
  ) {
    const conversion = await this.currenciesService.convertAmount(
      Number(amount),
      fromCurrency.toUpperCase(),
      toCurrency.toUpperCase(),
    );
    
    return {
      success: true,
      data: {
        originalAmount: Number(amount),
        fromCurrency: fromCurrency.toUpperCase(),
        toCurrency: toCurrency.toUpperCase(),
        convertedAmount: conversion.amount,
        exchangeRate: conversion.rate,
        timestamp: conversion.timestamp,
      },
    };
  }

  @Get('format/:amount/:currency')
  @ApiOperation({ summary: 'Format amount in currency format' })
  @ApiResponse({ status: 200, description: 'Formatted amount' })
  async formatAmount(
    @Param('amount') amount: number,
    @Param('currency') currency: string,
  ) {
    const formattedAmount = await this.currenciesService.formatAmount(
      Number(amount),
      currency.toUpperCase(),
    );
    
    return {
      success: true,
      data: {
        amount: Number(amount),
        currency: currency.toUpperCase(),
        formatted: formattedAmount,
      },
    };
  }
}
