import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { firstValueFrom } from 'rxjs';
import { Currency } from '../../database/entities/currency.entity';

@Injectable()
export class ExchangeRateService {
  private readonly logger = new Logger(ExchangeRateService.name);
  private readonly apiKey: string;
  private readonly baseUrl: string;
  private readonly baseCurrency: string = 'USD';
  private readonly updateInterval: number = 3600000; // 1 hour in milliseconds

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
    @InjectRepository(Currency)
    private readonly currencyRepository: Repository<Currency>,
  ) {
    this.apiKey = this.configService.get<string>('EXCHANGE_RATE_API_KEY');
    this.baseUrl = this.configService.get<string>('EXCHANGE_RATE_BASE_URL', 'https://api.exchangerate-api.com/v4');
    
    // Schedule regular exchange rate updates
    setInterval(() => this.updateAllExchangeRates(), this.updateInterval);
  }

  /**
   * Get exchange rate between two currencies
   */
  async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<number> {
    try {
      // Get currencies from database to ensure they're supported
      const fromCurrencyEntity = await this.currencyRepository.findOne({
        where: { code: fromCurrency, isActive: true },
      });

      const toCurrencyEntity = await this.currencyRepository.findOne({
        where: { code: toCurrency, isActive: true },
      });

      if (!fromCurrencyEntity || !toCurrencyEntity) {
        throw new Error('One or both currencies are not supported');
      }

      // If same currency, rate is 1
      if (fromCurrency === toCurrency) {
        return 1;
      }

      // Calculate cross rate using base currency rates
      const fromRate = Number(fromCurrencyEntity.exchangeRate);
      const toRate = Number(toCurrencyEntity.exchangeRate);

      // Calculate cross rate (through USD as base)
      const rate = toRate / fromRate;

      return rate;
    } catch (error) {
      this.logger.error(`Failed to get exchange rate: ${error.message}`, error.stack);
      throw new Error(`Failed to get exchange rate: ${error.message}`);
    }
  }

  /**
   * Convert amount from one currency to another
   */
  async convertAmount(
    amount: number,
    fromCurrency: string,
    toCurrency: string,
  ): Promise<{ amount: number; rate: number; timestamp: Date }> {
    try {
      const rate = await this.getExchangeRate(fromCurrency, toCurrency);
      const convertedAmount = amount * rate;

      return {
        amount: Math.round(convertedAmount * 100) / 100, // Round to 2 decimal places
        rate,
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to convert amount: ${error.message}`, error.stack);
      throw new Error(`Failed to convert amount: ${error.message}`);
    }
  }

  /**
   * Update exchange rates for all currencies
   */
  async updateAllExchangeRates(): Promise<void> {
    try {
      this.logger.log('Updating exchange rates for all currencies');

      // Get latest rates from API
      const rates = await this.fetchLatestRates();

      // Get all active currencies
      const currencies = await this.currencyRepository.find({
        where: { isActive: true },
      });

      // Update each currency's exchange rate
      for (const currency of currencies) {
        if (rates[currency.code]) {
          await this.currencyRepository.update(
            { code: currency.code },
            {
              exchangeRate: rates[currency.code],
              lastUpdated: new Date(),
            },
          );
        }
      }

      this.logger.log('Exchange rates updated successfully');
    } catch (error) {
      this.logger.error(`Failed to update exchange rates: ${error.message}`, error.stack);
    }
  }

  /**
   * Fetch latest exchange rates from API
   */
  private async fetchLatestRates(): Promise<Record<string, number>> {
    try {
      const response = await firstValueFrom(
        this.httpService.get(`${this.baseUrl}/latest/${this.baseCurrency}`, {
          params: {
            app_id: this.apiKey,
          },
        }),
      );

      return response.data.rates;
    } catch (error) {
      this.logger.error(`Failed to fetch exchange rates: ${error.message}`, error.stack);
      throw new Error(`Failed to fetch exchange rates: ${error.message}`);
    }
  }

  /**
   * Get supported currencies with exchange rates
   */
  async getSupportedCurrenciesWithRates(): Promise<Currency[]> {
    return this.currencyRepository.find({
      where: { isActive: true },
      order: { code: 'ASC' },
    });
  }

  /**
   * Get base currency
   */
  async getBaseCurrency(): Promise<Currency> {
    return this.currencyRepository.findOne({
      where: { isBaseCurrency: true },
    });
  }
}
