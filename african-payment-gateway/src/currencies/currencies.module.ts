import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { CurrenciesController } from './currencies.controller';
import { CurrenciesService } from './currencies.service';
import { ExchangeRateService } from './services/exchange-rate.service';
import { Currency } from '../database/entities/currency.entity';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [
    DatabaseModule,
    HttpModule,
    TypeOrmModule.forFeature([Currency]),
  ],
  controllers: [CurrenciesController],
  providers: [CurrenciesService, ExchangeRateService],
  exports: [CurrenciesService, ExchangeRateService],
})
export class CurrenciesModule {}
