import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { HttpModule } from '@nestjs/axios';
import { BullModule } from '@nestjs/bull';
import { NotificationsService } from './notifications.service';
import { WebhookService } from './services/webhook.service';
import { Webhook } from '../database/entities/webhook.entity';
import { DatabaseModule } from '../database/database.module';
import { CommonModule } from '../common/common.module';

@Module({
  imports: [
    DatabaseModule,
    CommonModule,
    HttpModule,
    TypeOrmModule.forFeature([Webhook]),
    BullModule.registerQueue({
      name: 'webhook-processing',
    }),
  ],
  providers: [NotificationsService, WebhookService],
  exports: [NotificationsService, WebhookService],
})
export class NotificationsModule {}
