import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Webhook } from '../../database/entities/webhook.entity';

@Injectable()
export class WebhookService {
  constructor(
    @InjectRepository(Webhook)
    private readonly webhookRepository: Repository<Webhook>,
  ) {}

  async createWebhook(webhookData: any) {
    const webhook = this.webhookRepository.create(webhookData);
    return this.webhookRepository.save(webhook);
  }

  async sendWebhook(webhookId: string) {
    // Placeholder for webhook sending logic
    console.log(`Sending webhook ${webhookId}`);
    return { success: true };
  }
}
