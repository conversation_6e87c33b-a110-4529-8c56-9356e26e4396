import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transaction, TransactionStatus } from '../database/entities/transaction.entity';
import { Webhook, WebhookStatus } from '../database/entities/webhook.entity';
import { TransactionService } from '../payments/services/transaction.service';
import { PaymentProviderFactory } from '../integrations/factories/payment-provider.factory';
import { NotificationsService } from '../notifications/notifications.service';

@Injectable()
export class WebhooksService {
  private readonly logger = new Logger(WebhooksService.name);

  constructor(
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    @InjectRepository(Webhook)
    private readonly webhookRepository: Repository<Webhook>,
    private readonly transactionService: TransactionService,
    private readonly paymentProviderFactory: PaymentProviderFactory,
    private readonly notificationsService: NotificationsService,
  ) {}

  async processMpesaWebhook(payload: any, headers: any): Promise<void> {
    try {
      const mpesaService = this.paymentProviderFactory.getProvider('mpesa' as any);
      const webhookData = await mpesaService.processWebhookNotification(payload);
      
      await this.updateTransactionFromWebhook(webhookData);
      this.logger.log(`M-Pesa webhook processed for transaction: ${webhookData.transactionReference}`);
    } catch (error) {
      this.logger.error('Failed to process M-Pesa webhook', error.stack);
      throw error;
    }
  }

  async processAirtelWebhook(payload: any, headers: any): Promise<void> {
    try {
      const airtelService = this.paymentProviderFactory.getProvider('airtel_money' as any);
      const webhookData = await airtelService.processWebhookNotification(payload);
      
      await this.updateTransactionFromWebhook(webhookData);
      this.logger.log(`Airtel Money webhook processed for transaction: ${webhookData.transactionReference}`);
    } catch (error) {
      this.logger.error('Failed to process Airtel Money webhook', error.stack);
      throw error;
    }
  }

  async processMtnWebhook(payload: any, headers: any): Promise<void> {
    try {
      const mtnService = this.paymentProviderFactory.getProvider('mtn_mobile_money' as any);
      const webhookData = await mtnService.processWebhookNotification(payload);
      
      await this.updateTransactionFromWebhook(webhookData);
      this.logger.log(`MTN Mobile Money webhook processed for transaction: ${webhookData.transactionReference}`);
    } catch (error) {
      this.logger.error('Failed to process MTN Mobile Money webhook', error.stack);
      throw error;
    }
  }

  async processCardWebhook(provider: string, payload: any, headers: any): Promise<void> {
    try {
      const cardService = this.paymentProviderFactory.getProvider('card' as any);
      const webhookData = await cardService.processWebhookNotification(payload);
      
      await this.updateTransactionFromWebhook(webhookData);
      this.logger.log(`${provider} card webhook processed for transaction: ${webhookData.transactionReference}`);
    } catch (error) {
      this.logger.error(`Failed to process ${provider} card webhook`, error.stack);
      throw error;
    }
  }

  private async updateTransactionFromWebhook(webhookData: {
    transactionReference: string;
    status: string;
    providerReference: string;
    amount?: number;
    currency?: string;
    metadata?: Record<string, any>;
  }): Promise<void> {
    try {
      // Find transaction by reference
      const transaction = await this.transactionRepository.findOne({
        where: { reference: webhookData.transactionReference },
        relations: ['merchant'],
      });

      if (!transaction) {
        this.logger.warn(`Transaction not found for reference: ${webhookData.transactionReference}`);
        return;
      }

      // Map webhook status to transaction status
      let transactionStatus: TransactionStatus;
      switch (webhookData.status) {
        case 'completed':
          transactionStatus = TransactionStatus.COMPLETED;
          break;
        case 'failed':
          transactionStatus = TransactionStatus.FAILED;
          break;
        case 'cancelled':
          transactionStatus = TransactionStatus.CANCELLED;
          break;
        case 'expired':
          transactionStatus = TransactionStatus.EXPIRED;
          break;
        default:
          transactionStatus = TransactionStatus.PROCESSING;
      }

      // Update transaction
      await this.transactionService.updateWithProviderResponse(
        transaction.id,
        webhookData.providerReference,
        webhookData.metadata || {},
        transactionStatus,
      );

      // Send merchant webhook notification
      await this.sendMerchantWebhook(transaction, transactionStatus, webhookData);

      this.logger.log(`Transaction ${transaction.reference} updated to status: ${transactionStatus}`);
    } catch (error) {
      this.logger.error('Failed to update transaction from webhook', error.stack);
      throw error;
    }
  }

  private async sendMerchantWebhook(
    transaction: Transaction,
    status: TransactionStatus,
    webhookData: any,
  ): Promise<void> {
    try {
      if (!transaction.callbackUrl) {
        this.logger.log(`No callback URL for transaction: ${transaction.reference}`);
        return;
      }

      const webhookPayload = {
        event: this.getWebhookEventName(status),
        timestamp: new Date().toISOString(),
        data: {
          reference: transaction.reference,
          status: status.toLowerCase(),
          amount: transaction.amount,
          currency: transaction.currency,
          paymentMethod: transaction.paymentMethod,
          customer: {
            name: transaction.customerName,
            email: transaction.customerEmail,
            phone: transaction.customerPhone,
          },
          providerReference: webhookData.providerReference,
          processedAt: new Date().toISOString(),
          metadata: transaction.metadata,
        },
      };

      // Create webhook record
      const webhook = this.webhookRepository.create({
        url: transaction.callbackUrl,
        event: webhookPayload.event as any,
        payload: webhookPayload,
        status: WebhookStatus.PENDING,
        merchantId: transaction.merchantId,
        transactionReference: transaction.reference,
      });

      await this.webhookRepository.save(webhook);

      // Send webhook (this would be handled by a background job in production)
      await this.notificationsService.sendNotification(
        'webhook',
        transaction.callbackUrl,
        webhookPayload,
      );

      this.logger.log(`Merchant webhook sent for transaction: ${transaction.reference}`);
    } catch (error) {
      this.logger.error('Failed to send merchant webhook', error.stack);
    }
  }

  private getWebhookEventName(status: TransactionStatus): string {
    switch (status) {
      case TransactionStatus.COMPLETED:
        return 'payment.completed';
      case TransactionStatus.FAILED:
        return 'payment.failed';
      case TransactionStatus.CANCELLED:
        return 'payment.cancelled';
      case TransactionStatus.EXPIRED:
        return 'payment.expired';
      default:
        return 'payment.updated';
    }
  }
}
