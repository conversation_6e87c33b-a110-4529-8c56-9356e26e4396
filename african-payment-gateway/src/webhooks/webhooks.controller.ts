import { Controller, Post, Body, Param, Headers, HttpCode, HttpStatus, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiExcludeEndpoint } from '@nestjs/swagger';
import { WebhooksService } from './webhooks.service';

@ApiTags('webhooks')
@Controller('webhooks')
export class WebhooksController {
  private readonly logger = new Logger(WebhooksController.name);

  constructor(private readonly webhooksService: WebhooksService) {}

  @Post('mpesa')
  @HttpCode(HttpStatus.OK)
  @ApiExcludeEndpoint() // Exclude from public API docs
  async handleMpesaWebhook(
    @Body() payload: any,
    @Headers() headers: any,
  ) {
    this.logger.log('Received M-Pesa webhook');
    
    try {
      await this.webhooksService.processMpesaWebhook(payload, headers);
      return { success: true, message: 'Webhook processed successfully' };
    } catch (error) {
      this.logger.error('M-Pesa webhook processing failed', error.stack);
      return { success: false, message: 'Webhook processing failed' };
    }
  }

  @Post('airtel')
  @HttpCode(HttpStatus.OK)
  @ApiExcludeEndpoint()
  async handleAirtelWebhook(
    @Body() payload: any,
    @Headers() headers: any,
  ) {
    this.logger.log('Received Airtel Money webhook');
    
    try {
      await this.webhooksService.processAirtelWebhook(payload, headers);
      return { success: true, message: 'Webhook processed successfully' };
    } catch (error) {
      this.logger.error('Airtel Money webhook processing failed', error.stack);
      return { success: false, message: 'Webhook processing failed' };
    }
  }

  @Post('mtn')
  @HttpCode(HttpStatus.OK)
  @ApiExcludeEndpoint()
  async handleMtnWebhook(
    @Body() payload: any,
    @Headers() headers: any,
  ) {
    this.logger.log('Received MTN Mobile Money webhook');
    
    try {
      await this.webhooksService.processMtnWebhook(payload, headers);
      return { success: true, message: 'Webhook processed successfully' };
    } catch (error) {
      this.logger.error('MTN Mobile Money webhook processing failed', error.stack);
      return { success: false, message: 'Webhook processing failed' };
    }
  }

  @Post('card/:provider')
  @HttpCode(HttpStatus.OK)
  @ApiExcludeEndpoint()
  async handleCardWebhook(
    @Param('provider') provider: string,
    @Body() payload: any,
    @Headers() headers: any,
  ) {
    this.logger.log(`Received ${provider} card webhook`);
    
    try {
      await this.webhooksService.processCardWebhook(provider, payload, headers);
      return { success: true, message: 'Webhook processed successfully' };
    } catch (error) {
      this.logger.error(`${provider} card webhook processing failed`, error.stack);
      return { success: false, message: 'Webhook processing failed' };
    }
  }

  @Post('test')
  @ApiOperation({ summary: 'Test webhook endpoint (development only)' })
  @ApiResponse({ status: 200, description: 'Test webhook processed' })
  async testWebhook(@Body() payload: any) {
    if (process.env.NODE_ENV === 'production') {
      return { success: false, message: 'Test endpoint not available in production' };
    }

    this.logger.log('Received test webhook', payload);
    return { success: true, message: 'Test webhook received', data: payload };
  }
}
