import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Merchant, MerchantStatus } from '../../database/entities/merchant.entity';
import { Currency } from '../../database/entities/currency.entity';
import { PaymentMethod } from '../../database/entities/transaction.entity';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { UtilsService } from '../../common/services/utils.service';

@Injectable()
export class PaymentValidationService {
  constructor(
    @InjectRepository(Merchant)
    private readonly merchantRepository: Repository<Merchant>,
    @InjectRepository(Currency)
    private readonly currencyRepository: Repository<Currency>,
    private readonly utilsService: UtilsService,
  ) {}

  /**
   * Validate payment request
   */
  async validatePaymentRequest(
    merchantId: string,
    createPaymentDto: CreatePaymentDto,
  ): Promise<void> {
    // Validate merchant
    await this.validateMerchant(merchantId);

    // Validate currency
    await this.validateCurrency(createPaymentDto.currency);

    // Validate amount
    this.validateAmount(createPaymentDto.amount, createPaymentDto.currency);

    // Validate payment method
    await this.validatePaymentMethod(
      merchantId,
      createPaymentDto.paymentMethod,
      createPaymentDto.currency,
    );

    // Validate customer details
    this.validateCustomerDetails(createPaymentDto);

    // Validate payment method specific details
    this.validatePaymentMethodDetails(createPaymentDto);

    // Validate transaction limits
    await this.validateTransactionLimits(merchantId, createPaymentDto.amount);
  }

  /**
   * Validate merchant
   */
  private async validateMerchant(merchantId: string): Promise<Merchant> {
    const merchant = await this.merchantRepository.findOne({
      where: { id: merchantId },
    });

    if (!merchant) {
      throw new BadRequestException('Merchant not found');
    }

    if (merchant.status !== MerchantStatus.ACTIVE) {
      throw new BadRequestException('Merchant account is not active');
    }

    if (!merchant.isKycCompleted) {
      throw new BadRequestException('Merchant KYC verification is incomplete');
    }

    return merchant;
  }

  /**
   * Validate currency
   */
  private async validateCurrency(currencyCode: string): Promise<Currency> {
    const currency = await this.currencyRepository.findOne({
      where: { code: currencyCode, isActive: true },
    });

    if (!currency) {
      throw new BadRequestException(`Currency ${currencyCode} is not supported`);
    }

    return currency;
  }

  /**
   * Validate amount
   */
  private validateAmount(amount: number, currency: string): void {
    if (amount <= 0) {
      throw new BadRequestException('Amount must be greater than zero');
    }

    // Currency-specific minimum amounts
    const minimumAmounts = {
      KES: 10,
      NGN: 50,
      GHS: 1,
      UGX: 1000,
      TZS: 1000,
      ZAR: 5,
      RWF: 100,
      ETB: 10,
      USD: 1,
    };

    const minimumAmount = minimumAmounts[currency] || 1;
    if (amount < minimumAmount) {
      throw new BadRequestException(
        `Minimum amount for ${currency} is ${minimumAmount}`,
      );
    }

    // Currency-specific maximum amounts
    const maximumAmounts = {
      KES: 1000000, // 1M KES
      NGN: 5000000, // 5M NGN
      GHS: 100000,  // 100K GHS
      UGX: 10000000, // 10M UGX
      TZS: 5000000,  // 5M TZS
      ZAR: 500000,   // 500K ZAR
      RWF: 10000000, // 10M RWF
      ETB: 1000000,  // 1M ETB
      USD: 50000,    // 50K USD
    };

    const maximumAmount = maximumAmounts[currency] || 50000;
    if (amount > maximumAmount) {
      throw new BadRequestException(
        `Maximum amount for ${currency} is ${maximumAmount}`,
      );
    }
  }

  /**
   * Validate payment method
   */
  private async validatePaymentMethod(
    merchantId: string,
    paymentMethod: PaymentMethod,
    currency: string,
  ): Promise<void> {
    const merchant = await this.merchantRepository.findOne({
      where: { id: merchantId },
    });

    // Check if payment method is allowed for merchant
    if (merchant.allowedPaymentMethods && 
        !merchant.allowedPaymentMethods.includes(paymentMethod)) {
      throw new BadRequestException(
        `Payment method ${paymentMethod} is not enabled for this merchant`,
      );
    }

    // Check if currency is supported for payment method
    const supportedCurrencies = this.getSupportedCurrenciesForPaymentMethod(paymentMethod);
    if (!supportedCurrencies.includes(currency)) {
      throw new BadRequestException(
        `Currency ${currency} is not supported for ${paymentMethod}`,
      );
    }
  }

  /**
   * Get supported currencies for payment method
   */
  private getSupportedCurrenciesForPaymentMethod(paymentMethod: PaymentMethod): string[] {
    const supportedCurrencies = {
      [PaymentMethod.MPESA]: ['KES', 'TZS'],
      [PaymentMethod.AIRTEL_MONEY]: ['KES', 'UGX', 'TZS', 'RWF'],
      [PaymentMethod.MTN_MOBILE_MONEY]: ['GHS', 'UGX', 'RWF'],
      [PaymentMethod.ORANGE_MONEY]: ['GHS', 'NGN'],
      [PaymentMethod.BANK_TRANSFER]: ['KES', 'NGN', 'GHS', 'UGX', 'TZS', 'ZAR', 'RWF', 'ETB', 'USD'],
      [PaymentMethod.CARD]: ['KES', 'NGN', 'GHS', 'UGX', 'TZS', 'ZAR', 'RWF', 'ETB', 'USD'],
      [PaymentMethod.WALLET]: ['USD', 'KES', 'NGN', 'GHS', 'ZAR'],
    };

    return supportedCurrencies[paymentMethod] || [];
  }

  /**
   * Validate customer details
   */
  private validateCustomerDetails(createPaymentDto: CreatePaymentDto): void {
    const { customer } = createPaymentDto;

    // Validate email
    if (!this.utilsService.isValidEmail(customer.email)) {
      throw new BadRequestException('Invalid email address');
    }

    // Validate phone number if provided
    if (customer.phone && !this.utilsService.isValidPhoneNumber(customer.phone)) {
      throw new BadRequestException('Invalid phone number format');
    }

    // For mobile money payments, phone number is required
    const mobileMethods = [
      PaymentMethod.MPESA,
      PaymentMethod.AIRTEL_MONEY,
      PaymentMethod.MTN_MOBILE_MONEY,
      PaymentMethod.ORANGE_MONEY,
    ];

    if (mobileMethods.includes(createPaymentDto.paymentMethod) && !customer.phone) {
      throw new BadRequestException('Phone number is required for mobile money payments');
    }

    // Validate phone number for specific mobile money providers
    if (createPaymentDto.paymentMethod === PaymentMethod.MPESA) {
      if (!this.utilsService.validateAfricanPhoneNumber(customer.phone, 'KE')) {
        throw new BadRequestException('Invalid Kenyan phone number for M-Pesa');
      }
    }
  }

  /**
   * Validate payment method specific details
   */
  private validatePaymentMethodDetails(createPaymentDto: CreatePaymentDto): void {
    const { paymentMethod, paymentDetails } = createPaymentDto;

    switch (paymentMethod) {
      case PaymentMethod.BANK_TRANSFER:
        if (!paymentDetails?.accountNumber) {
          throw new BadRequestException('Account number is required for bank transfers');
        }
        if (!paymentDetails?.bankCode) {
          throw new BadRequestException('Bank code is required for bank transfers');
        }
        break;

      case PaymentMethod.CARD:
        if (!paymentDetails?.cardToken) {
          throw new BadRequestException('Card token is required for card payments');
        }
        break;

      case PaymentMethod.WALLET:
        if (!paymentDetails?.walletProvider) {
          throw new BadRequestException('Wallet provider is required for wallet payments');
        }
        break;
    }
  }

  /**
   * Validate transaction limits
   */
  private async validateTransactionLimits(
    merchantId: string,
    amount: number,
  ): Promise<void> {
    const merchant = await this.merchantRepository.findOne({
      where: { id: merchantId },
    });

    // Check daily limit
    const dailyLimit = Number(merchant.dailyTransactionLimit);
    if (amount > dailyLimit) {
      throw new BadRequestException(
        `Transaction amount exceeds daily limit of ${dailyLimit}`,
      );
    }

    // Check monthly limit
    const monthlyLimit = Number(merchant.monthlyTransactionLimit);
    if (amount > monthlyLimit) {
      throw new BadRequestException(
        `Transaction amount exceeds monthly limit of ${monthlyLimit}`,
      );
    }

    // TODO: Check actual daily/monthly usage against limits
    // This would require querying transaction history
  }

  /**
   * Validate refund request
   */
  async validateRefundRequest(
    transactionId: string,
    refundAmount: number,
    reason?: string,
  ): Promise<void> {
    // TODO: Implement refund validation logic
    // - Check if transaction exists and is completed
    // - Check if refund amount is valid
    // - Check if partial refunds are allowed
    // - Check refund time limits
  }

  /**
   * Validate webhook URL
   */
  validateWebhookUrl(url: string): void {
    try {
      const parsedUrl = new URL(url);
      
      if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
        throw new BadRequestException('Webhook URL must use HTTP or HTTPS protocol');
      }

      if (parsedUrl.protocol === 'http:' && process.env.NODE_ENV === 'production') {
        throw new BadRequestException('HTTPS is required for webhook URLs in production');
      }

    } catch (error) {
      throw new BadRequestException('Invalid webhook URL format');
    }
  }
}
