import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transaction, TransactionStatus, TransactionType } from '../../database/entities/transaction.entity';
import { Merchant } from '../../database/entities/merchant.entity';
import { Currency } from '../../database/entities/currency.entity';
import { EncryptionService } from '../../common/services/encryption.service';
import { UtilsService } from '../../common/services/utils.service';
import { CreatePaymentDto } from '../dto/create-payment.dto';

@Injectable()
export class TransactionService {
  constructor(
    @InjectRepository(Transaction)
    private readonly transactionRepository: Repository<Transaction>,
    @InjectRepository(Merchant)
    private readonly merchantRepository: Repository<Merchant>,
    @InjectRepository(Currency)
    private readonly currencyRepository: Repository<Currency>,
    private readonly encryptionService: EncryptionService,
    private readonly utilsService: UtilsService,
  ) {}

  /**
   * Create a new transaction
   */
  async createTransaction(
    merchantId: string,
    createPaymentDto: CreatePaymentDto,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<Transaction> {
    // Validate merchant
    const merchant = await this.merchantRepository.findOne({
      where: { id: merchantId, status: 'active' },
    });

    if (!merchant) {
      throw new NotFoundException('Merchant not found or inactive');
    }

    // Validate currency
    const currency = await this.currencyRepository.findOne({
      where: { code: createPaymentDto.currency, isActive: true },
    });

    if (!currency) {
      throw new BadRequestException('Currency not supported');
    }

    // Validate payment method is allowed for merchant
    if (merchant.allowedPaymentMethods && 
        !merchant.allowedPaymentMethods.includes(createPaymentDto.paymentMethod)) {
      throw new BadRequestException('Payment method not allowed for this merchant');
    }

    // Validate currency is allowed for merchant
    if (merchant.allowedCurrencies && 
        !merchant.allowedCurrencies.includes(createPaymentDto.currency)) {
      throw new BadRequestException('Currency not allowed for this merchant');
    }

    // Generate transaction reference
    const reference = this.encryptionService.generateTransactionReference();

    // Calculate expiry time
    const expiryMinutes = createPaymentDto.expiryMinutes || 30;
    const expiresAt = new Date(Date.now() + expiryMinutes * 60 * 1000);

    // Create transaction
    const transaction = this.transactionRepository.create({
      reference,
      type: TransactionType.PAYMENT,
      status: TransactionStatus.PENDING,
      paymentMethod: createPaymentDto.paymentMethod,
      amount: createPaymentDto.amount,
      fee: 0, // Will be calculated later
      netAmount: createPaymentDto.amount, // Will be updated after fee calculation
      currency: createPaymentDto.currency,
      description: createPaymentDto.description,
      customerName: createPaymentDto.customer.name,
      customerEmail: createPaymentDto.customer.email,
      customerPhone: createPaymentDto.customer.phone,
      customerDetails: {
        address: createPaymentDto.customer.address,
        city: createPaymentDto.customer.city,
        country: createPaymentDto.customer.country,
        postalCode: createPaymentDto.customer.postalCode,
      },
      paymentDetails: createPaymentDto.paymentDetails,
      callbackUrl: createPaymentDto.callbackUrl,
      returnUrl: createPaymentDto.returnUrl,
      cancelUrl: createPaymentDto.cancelUrl,
      expiresAt,
      isTest: createPaymentDto.isTest || false,
      ipAddress,
      userAgent,
      merchantId,
      metadata: createPaymentDto.metadata,
    });

    return this.transactionRepository.save(transaction);
  }

  /**
   * Find transaction by reference
   */
  async findByReference(reference: string): Promise<Transaction> {
    const transaction = await this.transactionRepository.findOne({
      where: { reference },
      relations: ['merchant'],
    });

    if (!transaction) {
      throw new NotFoundException('Transaction not found');
    }

    return transaction;
  }

  /**
   * Find transaction by ID
   */
  async findById(id: string): Promise<Transaction> {
    const transaction = await this.transactionRepository.findOne({
      where: { id },
      relations: ['merchant'],
    });

    if (!transaction) {
      throw new NotFoundException('Transaction not found');
    }

    return transaction;
  }

  /**
   * Update transaction status
   */
  async updateStatus(
    transactionId: string,
    status: TransactionStatus,
    additionalData?: Partial<Transaction>,
  ): Promise<Transaction> {
    const transaction = await this.findById(transactionId);

    // Update status and additional data
    Object.assign(transaction, {
      status,
      ...additionalData,
      updatedAt: new Date(),
    });

    // Set processed timestamp for completed/failed transactions
    if (status === TransactionStatus.COMPLETED || status === TransactionStatus.FAILED) {
      transaction.processedAt = new Date();
    }

    return this.transactionRepository.save(transaction);
  }

  /**
   * Update transaction with provider response
   */
  async updateWithProviderResponse(
    transactionId: string,
    providerReference: string,
    providerResponse: Record<string, any>,
    status?: TransactionStatus,
  ): Promise<Transaction> {
    const updateData: Partial<Transaction> = {
      providerReference,
      providerResponse,
    };

    if (status) {
      updateData.status = status;
    }

    return this.updateStatus(transactionId, status || TransactionStatus.PROCESSING, updateData);
  }

  /**
   * Mark transaction as expired
   */
  async markAsExpired(transactionId: string): Promise<Transaction> {
    return this.updateStatus(transactionId, TransactionStatus.EXPIRED, {
      failureReason: 'Transaction expired',
    });
  }

  /**
   * Get transactions by merchant
   */
  async findByMerchant(
    merchantId: string,
    page: number = 1,
    limit: number = 20,
    status?: TransactionStatus,
  ): Promise<{ transactions: Transaction[]; total: number }> {
    const queryBuilder = this.transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.merchantId = :merchantId', { merchantId })
      .orderBy('transaction.createdAt', 'DESC');

    if (status) {
      queryBuilder.andWhere('transaction.status = :status', { status });
    }

    const [transactions, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();

    return { transactions, total };
  }

  /**
   * Get expired transactions
   */
  async findExpiredTransactions(): Promise<Transaction[]> {
    return this.transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.status = :status', { status: TransactionStatus.PENDING })
      .andWhere('transaction.expiresAt < :now', { now: new Date() })
      .getMany();
  }

  /**
   * Calculate transaction statistics
   */
  async getTransactionStats(
    merchantId: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<{
    totalTransactions: number;
    totalAmount: number;
    totalFees: number;
    successfulTransactions: number;
    failedTransactions: number;
    pendingTransactions: number;
  }> {
    const queryBuilder = this.transactionRepository
      .createQueryBuilder('transaction')
      .where('transaction.merchantId = :merchantId', { merchantId });

    if (startDate) {
      queryBuilder.andWhere('transaction.createdAt >= :startDate', { startDate });
    }

    if (endDate) {
      queryBuilder.andWhere('transaction.createdAt <= :endDate', { endDate });
    }

    const transactions = await queryBuilder.getMany();

    const stats = {
      totalTransactions: transactions.length,
      totalAmount: 0,
      totalFees: 0,
      successfulTransactions: 0,
      failedTransactions: 0,
      pendingTransactions: 0,
    };

    transactions.forEach(transaction => {
      stats.totalAmount += Number(transaction.amount);
      stats.totalFees += Number(transaction.fee);

      switch (transaction.status) {
        case TransactionStatus.COMPLETED:
          stats.successfulTransactions++;
          break;
        case TransactionStatus.FAILED:
          stats.failedTransactions++;
          break;
        case TransactionStatus.PENDING:
        case TransactionStatus.PROCESSING:
          stats.pendingTransactions++;
          break;
      }
    });

    return stats;
  }
}
