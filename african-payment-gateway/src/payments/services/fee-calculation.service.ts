import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Merchant } from '../../database/entities/merchant.entity';
import { PaymentMethod } from '../../database/entities/transaction.entity';
import { UtilsService } from '../../common/services/utils.service';

export interface FeeBreakdown {
  baseFee: number;
  processingFee: number;
  platformFee: number;
  totalFee: number;
  feeRate: number;
}

@Injectable()
export class FeeCalculationService {
  constructor(
    @InjectRepository(Merchant)
    private readonly merchantRepository: Repository<Merchant>,
    private readonly utilsService: UtilsService,
  ) {}

  /**
   * Calculate fees for a transaction
   */
  async calculateFees(
    amount: number,
    paymentMethod: PaymentMethod,
    currency: string,
    merchantId: string,
  ): Promise<FeeBreakdown> {
    const merchant = await this.merchantRepository.findOne({
      where: { id: merchantId },
    });

    if (!merchant) {
      throw new Error('Merchant not found');
    }

    // Get base fee rate from merchant settings
    const baseFeeRate = Number(merchant.transactionFeeRate) || 0.025; // 2.5% default
    const minimumFee = Number(merchant.minimumTransactionFee) || 0;

    // Get payment method specific fees
    const paymentMethodFees = this.getPaymentMethodFees(paymentMethod, currency);

    // Calculate base fee
    const baseFee = Math.max(amount * baseFeeRate, minimumFee);

    // Calculate processing fee (payment provider fees)
    const processingFee = amount * paymentMethodFees.processingRate + paymentMethodFees.fixedFee;

    // Calculate platform fee (our commission)
    const platformFee = amount * paymentMethodFees.platformRate;

    // Total fee
    const totalFee = baseFee + processingFee + platformFee;

    return {
      baseFee: this.roundToTwoDecimals(baseFee),
      processingFee: this.roundToTwoDecimals(processingFee),
      platformFee: this.roundToTwoDecimals(platformFee),
      totalFee: this.roundToTwoDecimals(totalFee),
      feeRate: totalFee / amount,
    };
  }

  /**
   * Get payment method specific fee structure
   */
  private getPaymentMethodFees(paymentMethod: PaymentMethod, currency: string): {
    processingRate: number;
    fixedFee: number;
    platformRate: number;
  } {
    const feeStructures = {
      [PaymentMethod.MPESA]: {
        KES: { processingRate: 0.015, fixedFee: 5, platformRate: 0.005 },
        USD: { processingRate: 0.02, fixedFee: 0.5, platformRate: 0.005 },
      },
      [PaymentMethod.AIRTEL_MONEY]: {
        KES: { processingRate: 0.018, fixedFee: 3, platformRate: 0.005 },
        UGX: { processingRate: 0.018, fixedFee: 100, platformRate: 0.005 },
        USD: { processingRate: 0.022, fixedFee: 0.3, platformRate: 0.005 },
      },
      [PaymentMethod.MTN_MOBILE_MONEY]: {
        GHS: { processingRate: 0.016, fixedFee: 0.5, platformRate: 0.005 },
        UGX: { processingRate: 0.016, fixedFee: 80, platformRate: 0.005 },
        RWF: { processingRate: 0.016, fixedFee: 50, platformRate: 0.005 },
        USD: { processingRate: 0.021, fixedFee: 0.4, platformRate: 0.005 },
      },
      [PaymentMethod.BANK_TRANSFER]: {
        KES: { processingRate: 0.01, fixedFee: 10, platformRate: 0.003 },
        NGN: { processingRate: 0.01, fixedFee: 50, platformRate: 0.003 },
        GHS: { processingRate: 0.01, fixedFee: 2, platformRate: 0.003 },
        ZAR: { processingRate: 0.01, fixedFee: 5, platformRate: 0.003 },
        USD: { processingRate: 0.015, fixedFee: 1, platformRate: 0.003 },
      },
      [PaymentMethod.CARD]: {
        KES: { processingRate: 0.025, fixedFee: 15, platformRate: 0.008 },
        NGN: { processingRate: 0.025, fixedFee: 100, platformRate: 0.008 },
        GHS: { processingRate: 0.025, fixedFee: 3, platformRate: 0.008 },
        ZAR: { processingRate: 0.025, fixedFee: 8, platformRate: 0.008 },
        USD: { processingRate: 0.029, fixedFee: 0.3, platformRate: 0.008 },
      },
      [PaymentMethod.WALLET]: {
        USD: { processingRate: 0.02, fixedFee: 0.5, platformRate: 0.005 },
        KES: { processingRate: 0.02, fixedFee: 10, platformRate: 0.005 },
        NGN: { processingRate: 0.02, fixedFee: 50, platformRate: 0.005 },
      },
    };

    const methodFees = feeStructures[paymentMethod];
    if (!methodFees) {
      // Default fee structure
      return { processingRate: 0.02, fixedFee: 0, platformRate: 0.005 };
    }

    const currencyFees = methodFees[currency];
    if (!currencyFees) {
      // Use USD as default if currency not found
      return methodFees['USD'] || { processingRate: 0.02, fixedFee: 0, platformRate: 0.005 };
    }

    return currencyFees;
  }

  /**
   * Calculate refund fees
   */
  async calculateRefundFees(
    originalAmount: number,
    refundAmount: number,
    paymentMethod: PaymentMethod,
    currency: string,
  ): Promise<FeeBreakdown> {
    // Refund fees are typically lower than payment fees
    const refundFeeRate = 0.005; // 0.5%
    const minimumRefundFee = this.getMinimumRefundFee(currency);

    const baseFee = Math.max(refundAmount * refundFeeRate, minimumRefundFee);
    const processingFee = 0; // No processing fee for refunds typically
    const platformFee = refundAmount * 0.002; // 0.2% platform fee

    const totalFee = baseFee + processingFee + platformFee;

    return {
      baseFee: this.roundToTwoDecimals(baseFee),
      processingFee: this.roundToTwoDecimals(processingFee),
      platformFee: this.roundToTwoDecimals(platformFee),
      totalFee: this.roundToTwoDecimals(totalFee),
      feeRate: totalFee / refundAmount,
    };
  }

  /**
   * Get minimum refund fee by currency
   */
  private getMinimumRefundFee(currency: string): number {
    const minimumFees = {
      KES: 5,
      NGN: 20,
      GHS: 1,
      UGX: 100,
      TZS: 100,
      ZAR: 2,
      RWF: 50,
      ETB: 5,
      USD: 0.5,
    };

    return minimumFees[currency] || 0.5;
  }

  /**
   * Calculate settlement fees (for payouts to merchants)
   */
  async calculateSettlementFees(
    amount: number,
    currency: string,
    settlementMethod: 'bank_transfer' | 'mobile_money',
  ): Promise<FeeBreakdown> {
    const settlementFeeRates = {
      bank_transfer: {
        KES: { rate: 0.005, fixedFee: 25 },
        NGN: { rate: 0.005, fixedFee: 100 },
        GHS: { rate: 0.005, fixedFee: 5 },
        USD: { rate: 0.008, fixedFee: 2 },
      },
      mobile_money: {
        KES: { rate: 0.008, fixedFee: 10 },
        UGX: { rate: 0.008, fixedFee: 200 },
        GHS: { rate: 0.008, fixedFee: 2 },
        USD: { rate: 0.01, fixedFee: 1 },
      },
    };

    const feeStructure = settlementFeeRates[settlementMethod]?.[currency] || 
                        { rate: 0.01, fixedFee: 1 };

    const baseFee = amount * feeStructure.rate + feeStructure.fixedFee;
    const processingFee = 0;
    const platformFee = 0;

    return {
      baseFee: this.roundToTwoDecimals(baseFee),
      processingFee: this.roundToTwoDecimals(processingFee),
      platformFee: this.roundToTwoDecimals(platformFee),
      totalFee: this.roundToTwoDecimals(baseFee),
      feeRate: baseFee / amount,
    };
  }

  /**
   * Round amount to two decimal places
   */
  private roundToTwoDecimals(amount: number): number {
    return Math.round(amount * 100) / 100;
  }

  /**
   * Get fee estimate for display purposes
   */
  async getFeeEstimate(
    amount: number,
    paymentMethod: PaymentMethod,
    currency: string,
    merchantId?: string,
  ): Promise<{ estimatedFee: number; netAmount: number }> {
    let feeBreakdown: FeeBreakdown;

    if (merchantId) {
      feeBreakdown = await this.calculateFees(amount, paymentMethod, currency, merchantId);
    } else {
      // Use default fee structure for estimates
      const defaultFees = this.getPaymentMethodFees(paymentMethod, currency);
      const estimatedFee = amount * (defaultFees.processingRate + defaultFees.platformRate) + 
                          defaultFees.fixedFee;
      
      feeBreakdown = {
        baseFee: 0,
        processingFee: this.roundToTwoDecimals(estimatedFee),
        platformFee: 0,
        totalFee: this.roundToTwoDecimals(estimatedFee),
        feeRate: estimatedFee / amount,
      };
    }

    return {
      estimatedFee: feeBreakdown.totalFee,
      netAmount: this.roundToTwoDecimals(amount - feeBreakdown.totalFee),
    };
  }
}
