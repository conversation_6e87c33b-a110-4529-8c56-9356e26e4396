import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { TransactionService } from './transaction.service';
import { FeeCalculationService } from './fee-calculation.service';
import { PaymentValidationService } from './payment-validation.service';
import { Transaction, TransactionStatus, PaymentMethod } from '../../database/entities/transaction.entity';
import { CreatePaymentDto } from '../dto/create-payment.dto';
import { UtilsService } from '../../common/services/utils.service';

export interface PaymentProcessingResult {
  success: boolean;
  transaction: Transaction;
  message: string;
  providerReference?: string;
  redirectUrl?: string;
}

@Injectable()
export class PaymentProcessorService {
  private readonly logger = new Logger(PaymentProcessorService.name);

  constructor(
    private readonly transactionService: TransactionService,
    private readonly feeCalculationService: FeeCalculationService,
    private readonly paymentValidationService: PaymentValidationService,
    private readonly utilsService: UtilsService,
    @InjectQueue('payment-processing') private readonly paymentQueue: Queue,
  ) {}

  /**
   * Initiate payment processing
   */
  async initiatePayment(
    merchantId: string,
    createPaymentDto: CreatePaymentDto,
    ipAddress?: string,
    userAgent?: string,
  ): Promise<PaymentProcessingResult> {
    try {
      this.logger.log(`Initiating payment for merchant ${merchantId}`);

      // Validate payment request
      await this.paymentValidationService.validatePaymentRequest(
        merchantId,
        createPaymentDto,
      );

      // Create transaction
      const transaction = await this.transactionService.createTransaction(
        merchantId,
        createPaymentDto,
        ipAddress,
        userAgent,
      );

      // Calculate fees
      const feeDetails = await this.feeCalculationService.calculateFees(
        transaction.amount,
        transaction.paymentMethod,
        transaction.currency,
        merchantId,
      );

      // Update transaction with fee information
      const updatedTransaction = await this.transactionService.updateStatus(
        transaction.id,
        TransactionStatus.PENDING,
        {
          fee: feeDetails.totalFee,
          netAmount: transaction.amount - feeDetails.totalFee,
        },
      );

      // Queue payment for processing
      await this.queuePaymentProcessing(updatedTransaction);

      this.logger.log(`Payment initiated successfully: ${transaction.reference}`);

      return {
        success: true,
        transaction: updatedTransaction,
        message: 'Payment initiated successfully',
      };
    } catch (error) {
      this.logger.error(`Payment initiation failed: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Process payment based on payment method
   */
  async processPayment(transaction: Transaction): Promise<PaymentProcessingResult> {
    try {
      this.logger.log(`Processing payment: ${transaction.reference}`);

      // Update status to processing
      await this.transactionService.updateStatus(
        transaction.id,
        TransactionStatus.PROCESSING,
      );

      let result: PaymentProcessingResult;

      // Route to appropriate payment processor based on method
      switch (transaction.paymentMethod) {
        case PaymentMethod.MPESA:
          result = await this.processMpesaPayment(transaction);
          break;
        case PaymentMethod.AIRTEL_MONEY:
          result = await this.processAirtelMoneyPayment(transaction);
          break;
        case PaymentMethod.MTN_MOBILE_MONEY:
          result = await this.processMtnMobileMoneyPayment(transaction);
          break;
        case PaymentMethod.BANK_TRANSFER:
          result = await this.processBankTransferPayment(transaction);
          break;
        case PaymentMethod.CARD:
          result = await this.processCardPayment(transaction);
          break;
        default:
          throw new BadRequestException(`Unsupported payment method: ${transaction.paymentMethod}`);
      }

      // Update transaction with result
      const status = result.success ? TransactionStatus.COMPLETED : TransactionStatus.FAILED;
      await this.transactionService.updateWithProviderResponse(
        transaction.id,
        result.providerReference || '',
        { processingResult: result },
        status,
      );

      this.logger.log(`Payment processing completed: ${transaction.reference} - ${status}`);

      return result;
    } catch (error) {
      this.logger.error(`Payment processing failed: ${transaction.reference}`, error.stack);

      // Update transaction as failed
      await this.transactionService.updateStatus(
        transaction.id,
        TransactionStatus.FAILED,
        {
          failureReason: error.message,
        },
      );

      return {
        success: false,
        transaction,
        message: error.message,
      };
    }
  }

  /**
   * Queue payment for background processing
   */
  private async queuePaymentProcessing(transaction: Transaction): Promise<void> {
    await this.paymentQueue.add(
      'process-payment',
      { transactionId: transaction.id },
      {
        delay: 1000, // 1 second delay
        attempts: 3,
        backoff: {
          type: 'exponential',
          delay: 2000,
        },
      },
    );
  }

  /**
   * Process M-Pesa payment
   */
  private async processMpesaPayment(transaction: Transaction): Promise<PaymentProcessingResult> {
    this.logger.log(`Processing M-Pesa payment: ${transaction.reference}`);

    // For now, simulate M-Pesa processing
    // In production, this would integrate with Safaricom's M-Pesa API
    const isSuccess = Math.random() > 0.1; // 90% success rate for simulation

    if (isSuccess) {
      return {
        success: true,
        transaction,
        message: 'M-Pesa payment processed successfully',
        providerReference: `MP${Date.now()}`,
      };
    } else {
      throw new Error('M-Pesa payment failed');
    }
  }

  /**
   * Process Airtel Money payment
   */
  private async processAirtelMoneyPayment(transaction: Transaction): Promise<PaymentProcessingResult> {
    this.logger.log(`Processing Airtel Money payment: ${transaction.reference}`);

    // Simulate Airtel Money processing
    const isSuccess = Math.random() > 0.15; // 85% success rate for simulation

    if (isSuccess) {
      return {
        success: true,
        transaction,
        message: 'Airtel Money payment processed successfully',
        providerReference: `AM${Date.now()}`,
      };
    } else {
      throw new Error('Airtel Money payment failed');
    }
  }

  /**
   * Process MTN Mobile Money payment
   */
  private async processMtnMobileMoneyPayment(transaction: Transaction): Promise<PaymentProcessingResult> {
    this.logger.log(`Processing MTN Mobile Money payment: ${transaction.reference}`);

    // Simulate MTN Mobile Money processing
    const isSuccess = Math.random() > 0.12; // 88% success rate for simulation

    if (isSuccess) {
      return {
        success: true,
        transaction,
        message: 'MTN Mobile Money payment processed successfully',
        providerReference: `MTN${Date.now()}`,
      };
    } else {
      throw new Error('MTN Mobile Money payment failed');
    }
  }

  /**
   * Process bank transfer payment
   */
  private async processBankTransferPayment(transaction: Transaction): Promise<PaymentProcessingResult> {
    this.logger.log(`Processing bank transfer payment: ${transaction.reference}`);

    // Simulate bank transfer processing
    const isSuccess = Math.random() > 0.05; // 95% success rate for simulation

    if (isSuccess) {
      return {
        success: true,
        transaction,
        message: 'Bank transfer payment processed successfully',
        providerReference: `BT${Date.now()}`,
      };
    } else {
      throw new Error('Bank transfer payment failed');
    }
  }

  /**
   * Process card payment
   */
  private async processCardPayment(transaction: Transaction): Promise<PaymentProcessingResult> {
    this.logger.log(`Processing card payment: ${transaction.reference}`);

    // Simulate card payment processing
    const isSuccess = Math.random() > 0.08; // 92% success rate for simulation

    if (isSuccess) {
      return {
        success: true,
        transaction,
        message: 'Card payment processed successfully',
        providerReference: `CD${Date.now()}`,
      };
    } else {
      throw new Error('Card payment failed');
    }
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(reference: string): Promise<Transaction> {
    return this.transactionService.findByReference(reference);
  }

  /**
   * Cancel payment
   */
  async cancelPayment(reference: string, reason?: string): Promise<Transaction> {
    const transaction = await this.transactionService.findByReference(reference);

    if (transaction.status !== TransactionStatus.PENDING) {
      throw new BadRequestException('Only pending payments can be cancelled');
    }

    return this.transactionService.updateStatus(
      transaction.id,
      TransactionStatus.CANCELLED,
      {
        failureReason: reason || 'Payment cancelled by user',
      },
    );
  }
}
