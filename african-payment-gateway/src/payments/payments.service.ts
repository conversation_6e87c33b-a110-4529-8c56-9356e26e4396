import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PaymentProcessorService } from './services/payment-processor.service';
import { TransactionService } from './services/transaction.service';
import { FeeCalculationService } from './services/fee-calculation.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { Transaction, TransactionStatus, PaymentMethod } from '../database/entities/transaction.entity';

@Injectable()
export class PaymentsService {
  constructor(
    private readonly paymentProcessorService: PaymentProcessorService,
    private readonly transactionService: TransactionService,
    private readonly feeCalculationService: FeeCalculationService,
  ) {}

  /**
   * Initiate a new payment
   */
  async initiatePayment(
    merchantId: string,
    createPaymentDto: CreatePaymentDto,
    ipAddress?: string,
    userAgent?: string,
  ) {
    const result = await this.paymentProcessorService.initiatePayment(
      merchantId,
      createPaymentDto,
      ipAddress,
      userAgent,
    );

    return {
      success: result.success,
      message: result.message,
      data: {
        reference: result.transaction.reference,
        status: result.transaction.status,
        amount: result.transaction.amount,
        currency: result.transaction.currency,
        fee: result.transaction.fee,
        netAmount: result.transaction.netAmount,
        paymentMethod: result.transaction.paymentMethod,
        expiresAt: result.transaction.expiresAt,
        redirectUrl: result.redirectUrl,
      },
    };
  }

  /**
   * Get payment status
   */
  async getPaymentStatus(reference: string, merchantId: string) {
    const transaction = await this.transactionService.findByReference(reference);

    // Ensure the transaction belongs to the requesting merchant
    if (transaction.merchantId !== merchantId) {
      throw new ForbiddenException('Access denied to this transaction');
    }

    return {
      success: true,
      data: {
        reference: transaction.reference,
        status: transaction.status,
        type: transaction.type,
        amount: transaction.amount,
        currency: transaction.currency,
        fee: transaction.fee,
        netAmount: transaction.netAmount,
        paymentMethod: transaction.paymentMethod,
        description: transaction.description,
        customer: {
          name: transaction.customerName,
          email: transaction.customerEmail,
          phone: transaction.customerPhone,
        },
        createdAt: transaction.createdAt,
        processedAt: transaction.processedAt,
        expiresAt: transaction.expiresAt,
        providerReference: transaction.providerReference,
        failureReason: transaction.failureReason,
        metadata: transaction.metadata,
      },
    };
  }

  /**
   * Cancel a payment
   */
  async cancelPayment(reference: string, merchantId: string, reason?: string) {
    const transaction = await this.transactionService.findByReference(reference);

    // Ensure the transaction belongs to the requesting merchant
    if (transaction.merchantId !== merchantId) {
      throw new ForbiddenException('Access denied to this transaction');
    }

    const cancelledTransaction = await this.paymentProcessorService.cancelPayment(
      reference,
      reason,
    );

    return {
      success: true,
      message: 'Payment cancelled successfully',
      data: {
        reference: cancelledTransaction.reference,
        status: cancelledTransaction.status,
        failureReason: cancelledTransaction.failureReason,
      },
    };
  }

  /**
   * Get merchant transactions
   */
  async getTransactions(
    merchantId: string,
    page: number = 1,
    limit: number = 20,
    status?: TransactionStatus,
  ) {
    const { transactions, total } = await this.transactionService.findByMerchant(
      merchantId,
      page,
      limit,
      status,
    );

    return {
      success: true,
      data: {
        transactions: transactions.map(transaction => ({
          reference: transaction.reference,
          status: transaction.status,
          type: transaction.type,
          amount: transaction.amount,
          currency: transaction.currency,
          fee: transaction.fee,
          netAmount: transaction.netAmount,
          paymentMethod: transaction.paymentMethod,
          description: transaction.description,
          customerName: transaction.customerName,
          customerEmail: transaction.customerEmail,
          createdAt: transaction.createdAt,
          processedAt: transaction.processedAt,
          providerReference: transaction.providerReference,
        })),
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
      },
    };
  }

  /**
   * Get payment statistics
   */
  async getPaymentStats(
    merchantId: string,
    startDate?: Date,
    endDate?: Date,
  ) {
    const stats = await this.transactionService.getTransactionStats(
      merchantId,
      startDate,
      endDate,
    );

    return {
      success: true,
      data: {
        totalTransactions: stats.totalTransactions,
        totalAmount: stats.totalAmount,
        totalFees: stats.totalFees,
        netAmount: stats.totalAmount - stats.totalFees,
        successfulTransactions: stats.successfulTransactions,
        failedTransactions: stats.failedTransactions,
        pendingTransactions: stats.pendingTransactions,
        successRate: stats.totalTransactions > 0 
          ? (stats.successfulTransactions / stats.totalTransactions) * 100 
          : 0,
        averageTransactionAmount: stats.totalTransactions > 0 
          ? stats.totalAmount / stats.totalTransactions 
          : 0,
      },
    };
  }

  /**
   * Get fee estimate
   */
  async getFeeEstimate(
    amount: number,
    paymentMethod: PaymentMethod,
    currency: string,
    merchantId: string,
  ) {
    const estimate = await this.feeCalculationService.getFeeEstimate(
      amount,
      paymentMethod,
      currency,
      merchantId,
    );

    return {
      success: true,
      data: {
        amount,
        currency,
        paymentMethod,
        estimatedFee: estimate.estimatedFee,
        netAmount: estimate.netAmount,
        feeRate: estimate.estimatedFee / amount,
      },
    };
  }
}
