import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsNumber,
  IsEmail,
  IsOptional,
  IsEnum,
  IsUrl,
  Min,
  Max,
  Length,
  IsObject,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PaymentMethod } from '../../database/entities/transaction.entity';

export class CustomerDetailsDto {
  @ApiProperty({ description: 'Customer full name' })
  @IsNotEmpty()
  @IsString()
  @Length(2, 100)
  name: string;

  @ApiProperty({ description: 'Customer email address' })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiProperty({ description: 'Customer phone number', required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ description: 'Customer address', required: false })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiProperty({ description: 'Customer city', required: false })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiProperty({ description: 'Customer country code', required: false })
  @IsOptional()
  @IsString()
  @Length(2, 3)
  country?: string;

  @ApiProperty({ description: 'Customer postal code', required: false })
  @IsOptional()
  @IsString()
  postalCode?: string;
}

export class PaymentDetailsDto {
  @ApiProperty({ description: 'Account number for mobile money or bank transfer', required: false })
  @IsOptional()
  @IsString()
  accountNumber?: string;

  @ApiProperty({ description: 'Account name', required: false })
  @IsOptional()
  @IsString()
  accountName?: string;

  @ApiProperty({ description: 'Bank code for bank transfers', required: false })
  @IsOptional()
  @IsString()
  bankCode?: string;

  @ApiProperty({ description: 'Card token for card payments', required: false })
  @IsOptional()
  @IsString()
  cardToken?: string;

  @ApiProperty({ description: 'Wallet provider for wallet payments', required: false })
  @IsOptional()
  @IsString()
  walletProvider?: string;
}

export class CreatePaymentDto {
  @ApiProperty({ description: 'Payment amount', example: 1000.50 })
  @IsNotEmpty()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  @Max(********)
  amount: number;

  @ApiProperty({ description: 'Currency code', example: 'KES' })
  @IsNotEmpty()
  @IsString()
  @Length(3, 3)
  currency: string;

  @ApiProperty({ 
    description: 'Payment method',
    enum: PaymentMethod,
    example: PaymentMethod.MPESA
  })
  @IsNotEmpty()
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty({ description: 'Payment description', required: false })
  @IsOptional()
  @IsString()
  @Length(0, 500)
  description?: string;

  @ApiProperty({ description: 'Customer details' })
  @IsNotEmpty()
  @ValidateNested()
  @Type(() => CustomerDetailsDto)
  customer: CustomerDetailsDto;

  @ApiProperty({ description: 'Payment method specific details', required: false })
  @IsOptional()
  @ValidateNested()
  @Type(() => PaymentDetailsDto)
  paymentDetails?: PaymentDetailsDto;

  @ApiProperty({ description: 'Callback URL for payment notifications', required: false })
  @IsOptional()
  @IsUrl()
  callbackUrl?: string;

  @ApiProperty({ description: 'Return URL after successful payment', required: false })
  @IsOptional()
  @IsUrl()
  returnUrl?: string;

  @ApiProperty({ description: 'Cancel URL for cancelled payments', required: false })
  @IsOptional()
  @IsUrl()
  cancelUrl?: string;

  @ApiProperty({ description: 'Payment expiry time in minutes', required: false, default: 30 })
  @IsOptional()
  @IsNumber()
  @Min(5)
  @Max(1440) // 24 hours
  expiryMinutes?: number;

  @ApiProperty({ description: 'Additional metadata', required: false })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiProperty({ description: 'Test mode flag', required: false, default: false })
  @IsOptional()
  isTest?: boolean;
}
