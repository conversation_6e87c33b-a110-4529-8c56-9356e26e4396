import { Processor, Process } from '@nestjs/bull';
import { Logger } from '@nestjs/common';
import { Job } from 'bull';
import { PaymentProcessorService } from '../services/payment-processor.service';
import { TransactionService } from '../services/transaction.service';

@Processor('payment-processing')
export class PaymentProcessor {
  private readonly logger = new Logger(PaymentProcessor.name);

  constructor(
    private readonly paymentProcessorService: PaymentProcessorService,
    private readonly transactionService: TransactionService,
  ) {}

  @Process('process-payment')
  async handlePaymentProcessing(job: Job<{ transactionId: string }>) {
    const { transactionId } = job.data;
    
    this.logger.log(`Processing payment job for transaction: ${transactionId}`);

    try {
      // Get transaction details
      const transaction = await this.transactionService.findById(transactionId);
      
      // Process the payment
      const result = await this.paymentProcessorService.processPayment(transaction);
      
      this.logger.log(`Payment processing completed for transaction: ${transactionId}, success: ${result.success}`);
      
      return result;
    } catch (error) {
      this.logger.error(`Payment processing failed for transaction: ${transactionId}`, error.stack);
      throw error;
    }
  }

  @Process('expire-transactions')
  async handleTransactionExpiry(job: Job) {
    this.logger.log('Processing expired transactions');

    try {
      const expiredTransactions = await this.transactionService.findExpiredTransactions();
      
      for (const transaction of expiredTransactions) {
        await this.transactionService.markAsExpired(transaction.id);
        this.logger.log(`Marked transaction as expired: ${transaction.reference}`);
      }

      this.logger.log(`Processed ${expiredTransactions.length} expired transactions`);
    } catch (error) {
      this.logger.error('Failed to process expired transactions', error.stack);
      throw error;
    }
  }
}
