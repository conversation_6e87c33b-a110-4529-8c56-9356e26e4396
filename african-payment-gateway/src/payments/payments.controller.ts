import {
  Controller,
  Post,
  Get,
  Param,
  Body,
  Query,
  Req,
  UseGuards,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { Request } from 'express';
import { PaymentsService } from './payments.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ApiKeyGuard } from '../auth/guards/api-key.guard';
import { CurrentMerchant } from '../auth/decorators/current-merchant.decorator';
import { Merchant } from '../database/entities/merchant.entity';

@ApiTags('payments')
@Controller('payments')
export class PaymentsController {
  constructor(private readonly paymentsService: PaymentsService) {}

  @Post()
  @UseGuards(ApiKeyGuard)
  @ApiOperation({ summary: 'Initiate a new payment' })
  @ApiResponse({ 
    status: HttpStatus.CREATED, 
    description: 'Payment initiated successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Invalid payment request' 
  })
  async createPayment(
    @Body() createPaymentDto: CreatePaymentDto,
    @CurrentMerchant() merchant: Merchant,
    @Req() request: Request,
  ) {
    const ipAddress = request.ip || request.connection.remoteAddress;
    const userAgent = request.get('User-Agent');

    return this.paymentsService.initiatePayment(
      merchant.id,
      createPaymentDto,
      ipAddress,
      userAgent,
    );
  }

  @Get(':reference')
  @UseGuards(ApiKeyGuard)
  @ApiOperation({ summary: 'Get payment status by reference' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Payment details retrieved successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.NOT_FOUND, 
    description: 'Payment not found' 
  })
  async getPaymentStatus(
    @Param('reference') reference: string,
    @CurrentMerchant() merchant: Merchant,
  ) {
    return this.paymentsService.getPaymentStatus(reference, merchant.id);
  }

  @Post(':reference/cancel')
  @UseGuards(ApiKeyGuard)
  @ApiOperation({ summary: 'Cancel a pending payment' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Payment cancelled successfully' 
  })
  @ApiResponse({ 
    status: HttpStatus.BAD_REQUEST, 
    description: 'Payment cannot be cancelled' 
  })
  async cancelPayment(
    @Param('reference') reference: string,
    @Body('reason') reason: string,
    @CurrentMerchant() merchant: Merchant,
  ) {
    return this.paymentsService.cancelPayment(reference, merchant.id, reason);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get merchant transactions' })
  @ApiQuery({ name: 'page', required: false, type: Number })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'status', required: false, type: String })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Transactions retrieved successfully' 
  })
  async getTransactions(
    @CurrentMerchant() merchant: Merchant,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 20,
    @Query('status') status?: string,
  ) {
    return this.paymentsService.getTransactions(
      merchant.id,
      page,
      limit,
      status as any,
    );
  }

  @Get('stats/summary')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get payment statistics summary' })
  @ApiQuery({ name: 'startDate', required: false, type: String })
  @ApiQuery({ name: 'endDate', required: false, type: String })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Statistics retrieved successfully' 
  })
  async getPaymentStats(
    @CurrentMerchant() merchant: Merchant,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;

    return this.paymentsService.getPaymentStats(merchant.id, start, end);
  }

  @Post('fees/estimate')
  @UseGuards(ApiKeyGuard)
  @ApiOperation({ summary: 'Get fee estimate for a payment' })
  @ApiResponse({ 
    status: HttpStatus.OK, 
    description: 'Fee estimate calculated successfully' 
  })
  async getFeeEstimate(
    @Body() estimateDto: {
      amount: number;
      paymentMethod: string;
      currency: string;
    },
    @CurrentMerchant() merchant: Merchant,
  ) {
    return this.paymentsService.getFeeEstimate(
      estimateDto.amount,
      estimateDto.paymentMethod as any,
      estimateDto.currency,
      merchant.id,
    );
  }
}
