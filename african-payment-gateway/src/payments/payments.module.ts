import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { PaymentsController } from './payments.controller';
import { PaymentsService } from './payments.service';
import { TransactionService } from './services/transaction.service';
import { PaymentProcessorService } from './services/payment-processor.service';
import { PaymentValidationService } from './services/payment-validation.service';
import { FeeCalculationService } from './services/fee-calculation.service';
import { PaymentProcessor } from './processors/payment.processor';
import { Transaction } from '../database/entities/transaction.entity';
import { Merchant } from '../database/entities/merchant.entity';
import { Currency } from '../database/entities/currency.entity';
import { DatabaseModule } from '../database/database.module';

@Module({
  imports: [
    DatabaseModule,
    TypeOrmModule.forFeature([Transaction, Merchant, Currency]),
    BullModule.registerQueue({
      name: 'payment-processing',
    }),
  ],
  controllers: [PaymentsController],
  providers: [
    PaymentsService,
    TransactionService,
    PaymentProcessorService,
    PaymentValidationService,
    FeeCalculationService,
    PaymentProcessor,
  ],
  exports: [
    PaymentsService,
    TransactionService,
    PaymentProcessorService,
  ],
})
export class PaymentsModule {}
