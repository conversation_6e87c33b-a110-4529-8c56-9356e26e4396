import { Controller, Get, Post, Body, Param, Put, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { MerchantsService } from './merchants.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@ApiTags('merchants')
@Controller('merchants')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class MerchantsController {
  constructor(private readonly merchantsService: MerchantsService) {}

  @Get()
  @ApiOperation({ summary: 'Get all merchants' })
  findAll() {
    return this.merchantsService.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get merchant by ID' })
  findOne(@Param('id') id: string) {
    return this.merchantsService.findOne(id);
  }

  @Post()
  @ApiOperation({ summary: 'Create new merchant' })
  create(@Body() createMerchantDto: any) {
    return this.merchantsService.create(createMerchantDto);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update merchant' })
  update(@Param('id') id: string, @Body() updateMerchantDto: any) {
    return this.merchantsService.update(id, updateMerchantDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete merchant' })
  remove(@Param('id') id: string) {
    return this.merchantsService.remove(id);
  }
}
