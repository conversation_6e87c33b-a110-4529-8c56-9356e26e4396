import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Merchant } from '../database/entities/merchant.entity';

@Injectable()
export class MerchantsService {
  constructor(
    @InjectRepository(Merchant)
    private readonly merchantRepository: Repository<Merchant>,
  ) {}

  async findAll() {
    return this.merchantRepository.find();
  }

  async findOne(id: string) {
    return this.merchantRepository.findOne({ where: { id } });
  }

  async create(merchantData: any) {
    const merchant = this.merchantRepository.create(merchantData);
    return this.merchantRepository.save(merchant);
  }

  async update(id: string, updateData: any) {
    await this.merchantRepository.update(id, updateData);
    return this.findOne(id);
  }

  async remove(id: string) {
    return this.merchantRepository.softDelete(id);
  }
}
