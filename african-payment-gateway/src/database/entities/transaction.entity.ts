import { Entity, Column, ManyToOne, OneToMany, Index, JoinColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Merchant } from './merchant.entity';

export enum TransactionType {
  PAYMENT = 'payment',
  REFUND = 'refund',
  PAYOUT = 'payout',
  TRANSFER = 'transfer',
}

export enum TransactionStatus {
  PENDING = 'pending',
  PROCESSING = 'processing',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
}

export enum PaymentMethod {
  MPESA = 'mpesa',
  AIRTEL_MONEY = 'airtel_money',
  MTN_MOBILE_MONEY = 'mtn_mobile_money',
  ORANGE_MONEY = 'orange_money',
  BANK_TRANSFER = 'bank_transfer',
  CARD = 'card',
  WALLET = 'wallet',
}

@Entity('transactions')
@Index(['merchantId', 'status'])
@Index(['externalTransactionId'], { unique: true })
@Index(['reference'], { unique: true })
export class Transaction extends BaseEntity {
  @Column({ type: 'varchar', length: 100, unique: true })
  reference: string;

  @Column({ type: 'varchar', length: 255, unique: true, nullable: true })
  externalTransactionId?: string;

  @Column({
    type: 'enum',
    enum: TransactionType,
    default: TransactionType.PAYMENT,
  })
  type: TransactionType;

  @Column({
    type: 'enum',
    enum: TransactionStatus,
    default: TransactionStatus.PENDING,
  })
  status: TransactionStatus;

  @Column({
    type: 'enum',
    enum: PaymentMethod,
  })
  paymentMethod: PaymentMethod;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  amount: number;

  @Column({ type: 'decimal', precision: 15, scale: 2, default: 0 })
  fee: number;

  @Column({ type: 'decimal', precision: 15, scale: 2 })
  netAmount: number;

  @Column({ type: 'varchar', length: 3 })
  currency: string;

  @Column({ type: 'decimal', precision: 10, scale: 6, default: 1 })
  exchangeRate: number;

  @Column({ type: 'varchar', length: 3, nullable: true })
  baseCurrency?: string;

  @Column({ type: 'decimal', precision: 15, scale: 2, nullable: true })
  baseAmount?: number;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  customerName?: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  customerEmail?: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  customerPhone?: string;

  @Column({ type: 'jsonb', nullable: true })
  customerDetails?: {
    address?: string;
    city?: string;
    country?: string;
    postalCode?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  paymentDetails?: {
    accountNumber?: string;
    accountName?: string;
    bankCode?: string;
    cardLast4?: string;
    cardBrand?: string;
    walletProvider?: string;
  };

  @Column({ type: 'varchar', length: 500, nullable: true })
  callbackUrl?: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  returnUrl?: string;

  @Column({ type: 'varchar', length: 500, nullable: true })
  cancelUrl?: string;

  @Column({ type: 'timestamp with time zone', nullable: true })
  expiresAt?: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  processedAt?: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  settledAt?: Date;

  @Column({ type: 'varchar', length: 255, nullable: true })
  failureReason?: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  providerReference?: string;

  @Column({ type: 'jsonb', nullable: true })
  providerResponse?: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  @Column({ type: 'boolean', default: false })
  isTest: boolean;

  @Column({ type: 'varchar', length: 45, nullable: true })
  ipAddress?: string;

  @Column({ type: 'text', nullable: true })
  userAgent?: string;

  // Relationships
  @ManyToOne(() => Merchant, (merchant) => merchant.transactions)
  @JoinColumn({ name: 'merchantId' })
  merchant: Merchant;

  @Column({ type: 'uuid' })
  merchantId: string;

  // Note: Additional relationships will be added when other entities are created

  // Virtual fields
  get isCompleted(): boolean {
    return this.status === TransactionStatus.COMPLETED;
  }

  get isFailed(): boolean {
    return this.status === TransactionStatus.FAILED;
  }

  get isPending(): boolean {
    return this.status === TransactionStatus.PENDING;
  }

  // Note: Additional virtual fields will be added when refund entity is created
}
