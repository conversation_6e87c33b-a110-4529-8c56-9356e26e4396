import { Entity, Column, Index } from 'typeorm';
import { BaseEntity } from './base.entity';

@Entity('currencies')
@Index(['code'], { unique: true })
export class Currency extends BaseEntity {
  @Column({ type: 'varchar', length: 3, unique: true })
  code: string;

  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({ type: 'varchar', length: 10 })
  symbol: string;

  @Column({ type: 'int', default: 2 })
  decimalPlaces: number;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column({ type: 'boolean', default: false })
  isBaseCurrency: boolean;

  @Column({ type: 'decimal', precision: 10, scale: 6, default: 1 })
  exchangeRate: number;

  @Column({ type: 'timestamp with time zone', nullable: true })
  lastUpdated?: Date;

  @Column({ type: 'varchar', length: 3 })
  country: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: {
    region?: string;
    centralBank?: string;
    supportedPaymentMethods?: string[];
  };
}
