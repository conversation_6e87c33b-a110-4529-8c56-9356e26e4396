import { Entity, Column, ManyToOne, Index, JoinColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Merchant } from './merchant.entity';

export enum ApiKeyType {
  PUBLIC = 'public',
  SECRET = 'secret',
  WEBHOOK = 'webhook',
}

export enum ApiKeyStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  REVOKED = 'revoked',
}

@Entity('api_keys')
@Index(['keyHash'], { unique: true })
export class ApiKey extends BaseEntity {
  @Column({ type: 'varchar', length: 100 })
  name: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  keyHash: string;

  @Column({ type: 'varchar', length: 50 })
  keyPrefix: string;

  @Column({
    type: 'enum',
    enum: ApiKeyType,
    default: ApiKeyType.SECRET,
  })
  type: ApiKeyType;

  @Column({
    type: 'enum',
    enum: ApiKeyStatus,
    default: ApiKeyStatus.ACTIVE,
  })
  status: ApiKeyStatus;

  @Column({ type: 'jsonb', nullable: true })
  permissions?: string[];

  @Column({ type: 'jsonb', nullable: true })
  ipWhitelist?: string[];

  @Column({ type: 'timestamp with time zone', nullable: true })
  expiresAt?: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  lastUsedAt?: Date;

  @Column({ type: 'int', default: 0 })
  usageCount: number;

  @Column({ type: 'int', nullable: true })
  rateLimit?: number;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => Merchant, (merchant) => merchant.apiKeys)
  @JoinColumn({ name: 'merchantId' })
  merchant: Merchant;

  @Column({ type: 'uuid' })
  merchantId: string;

  // Virtual fields
  get isActive(): boolean {
    return this.status === ApiKeyStatus.ACTIVE;
  }

  get isExpired(): boolean {
    return this.expiresAt ? new Date() > this.expiresAt : false;
  }
}
