import { Entity, Column, ManyToOne, Index, JoinColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Merchant } from './merchant.entity';

export enum WebhookStatus {
  PENDING = 'pending',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  RETRYING = 'retrying',
  DISABLED = 'disabled',
}

export enum WebhookEvent {
  PAYMENT_COMPLETED = 'payment.completed',
  PAYMENT_FAILED = 'payment.failed',
  PAYMENT_CANCELLED = 'payment.cancelled',
  REFUND_COMPLETED = 'refund.completed',
  REFUND_FAILED = 'refund.failed',
  PAYOUT_COMPLETED = 'payout.completed',
  PAYOUT_FAILED = 'payout.failed',
  MERCHANT_VERIFIED = 'merchant.verified',
  MERCHANT_SUSPENDED = 'merchant.suspended',
}

@Entity('webhooks')
@Index(['merchantId', 'event'])
@Index(['status', 'nextRetryAt'])
export class Webhook extends BaseEntity {
  @Column({ type: 'varchar', length: 500 })
  url: string;

  @Column({
    type: 'enum',
    enum: WebhookEvent,
  })
  event: WebhookEvent;

  @Column({
    type: 'enum',
    enum: WebhookStatus,
    default: WebhookStatus.PENDING,
  })
  status: WebhookStatus;

  @Column({ type: 'jsonb' })
  payload: Record<string, any>;

  @Column({ type: 'varchar', length: 255, nullable: true })
  signature?: string;

  @Column({ type: 'int', default: 0 })
  attemptCount: number;

  @Column({ type: 'int', default: 3 })
  maxAttempts: number;

  @Column({ type: 'timestamp with time zone', nullable: true })
  nextRetryAt?: Date;

  @Column({ type: 'timestamp with time zone', nullable: true })
  deliveredAt?: Date;

  @Column({ type: 'int', nullable: true })
  responseStatus?: number;

  @Column({ type: 'text', nullable: true })
  responseBody?: string;

  @Column({ type: 'text', nullable: true })
  errorMessage?: string;

  @Column({ type: 'jsonb', nullable: true })
  headers?: Record<string, string>;

  @Column({ type: 'int', default: 30000 })
  timeoutMs: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  transactionReference?: string;

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => Merchant, (merchant) => merchant.webhooks)
  @JoinColumn({ name: 'merchantId' })
  merchant: Merchant;

  @Column({ type: 'uuid' })
  merchantId: string;

  // Virtual fields
  get canRetry(): boolean {
    return this.status === WebhookStatus.FAILED && this.attemptCount < this.maxAttempts;
  }

  get isDelivered(): boolean {
    return this.status === WebhookStatus.DELIVERED;
  }

  get shouldRetry(): boolean {
    return this.canRetry && this.nextRetryAt && new Date() >= this.nextRetryAt;
  }
}
