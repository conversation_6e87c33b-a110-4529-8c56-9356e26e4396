import { Entity, Column, ManyToOne, OneToMany, Index, JoinColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { User } from './user.entity';
import { Transaction } from './transaction.entity';
import { Webhook } from './webhook.entity';
import { Api<PERSON>ey } from './api-key.entity';

export enum MerchantStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  SUSPENDED = 'suspended',
  REJECTED = 'rejected',
}

export enum KycStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
}

export enum BusinessType {
  SOLE_PROPRIETORSHIP = 'sole_proprietorship',
  PARTNERSHIP = 'partnership',
  LIMITED_COMPANY = 'limited_company',
  NGO = 'ngo',
  GOVERNMENT = 'government',
  OTHER = 'other',
}

@Entity('merchants')
@Index(['businessRegistrationNumber'], { unique: true })
@Index(['taxIdentificationNumber'], { unique: true })
export class Merchant extends BaseEntity {
  @Column({ type: 'varchar', length: 255 })
  businessName: string;

  @Column({ type: 'varchar', length: 255, unique: true })
  businessRegistrationNumber: string;

  @Column({ type: 'varchar', length: 255, unique: true, nullable: true })
  taxIdentificationNumber?: string;

  @Column({
    type: 'enum',
    enum: BusinessType,
    default: BusinessType.SOLE_PROPRIETORSHIP,
  })
  businessType: BusinessType;

  @Column({ type: 'text', nullable: true })
  businessDescription?: string;

  @Column({ type: 'varchar', length: 255 })
  businessAddress: string;

  @Column({ type: 'varchar', length: 100 })
  businessCity: string;

  @Column({ type: 'varchar', length: 100 })
  businessState: string;

  @Column({ type: 'varchar', length: 3 })
  businessCountry: string;

  @Column({ type: 'varchar', length: 20, nullable: true })
  businessPostalCode?: string;

  @Column({ type: 'varchar', length: 255 })
  contactEmail: string;

  @Column({ type: 'varchar', length: 20 })
  contactPhone: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  website?: string;

  @Column({
    type: 'enum',
    enum: MerchantStatus,
    default: MerchantStatus.PENDING,
  })
  status: MerchantStatus;

  @Column({
    type: 'enum',
    enum: KycStatus,
    default: KycStatus.NOT_STARTED,
  })
  kycStatus: KycStatus;

  @Column({ type: 'timestamp with time zone', nullable: true })
  kycCompletedAt?: Date;

  @Column({ type: 'decimal', precision: 5, scale: 4, default: 0.025 })
  transactionFeeRate: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  minimumTransactionFee: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 1000000 })
  dailyTransactionLimit: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 5000000 })
  monthlyTransactionLimit: number;

  @Column({ type: 'jsonb', nullable: true })
  allowedPaymentMethods?: string[];

  @Column({ type: 'jsonb', nullable: true })
  allowedCurrencies?: string[];

  @Column({ type: 'varchar', length: 255, nullable: true })
  logoUrl?: string;

  @Column({ type: 'jsonb', nullable: true })
  bankDetails?: {
    bankName: string;
    accountNumber: string;
    accountName: string;
    swiftCode?: string;
    routingNumber?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  documents?: {
    businessLicense?: string;
    taxCertificate?: string;
    bankStatement?: string;
    identityDocument?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  settings?: {
    webhookUrl?: string;
    returnUrl?: string;
    cancelUrl?: string;
    notificationEmail?: string;
    autoSettle?: boolean;
    settlementSchedule?: string;
  };

  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;

  // Relationships
  @ManyToOne(() => User, (user) => user.merchants)
  @JoinColumn({ name: 'ownerId' })
  owner: User;

  @Column({ type: 'uuid' })
  ownerId: string;

  @OneToMany(() => Transaction, (transaction) => transaction.merchant)
  transactions: Transaction[];

  @OneToMany(() => Webhook, (webhook) => webhook.merchant)
  webhooks: Webhook[];

  @OneToMany(() => ApiKey, (apiKey) => apiKey.merchant)
  apiKeys: ApiKey[];

  // Virtual fields
  get isActive(): boolean {
    return this.status === MerchantStatus.ACTIVE;
  }

  get isKycCompleted(): boolean {
    return this.kycStatus === KycStatus.COMPLETED;
  }
}
