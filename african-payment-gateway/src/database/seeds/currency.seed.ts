import { DataSource } from 'typeorm';
import { Currency } from '../entities/currency.entity';

export async function seedCurrencies(dataSource: DataSource): Promise<void> {
  const currencyRepository = dataSource.getRepository(Currency);

  const currencies = [
    {
      code: 'KES',
      name: 'Kenyan Shilling',
      symbol: 'KSh',
      decimalPlaces: 2,
      isActive: true,
      isBaseCurrency: false,
      exchangeRate: 1,
      country: 'KEN',
      metadata: {
        region: 'East Africa',
        centralBank: 'Central Bank of Kenya',
        supportedPaymentMethods: ['mpesa', 'airtel_money', 'bank_transfer', 'card'],
      },
    },
    {
      code: 'NGN',
      name: 'Nigerian Naira',
      symbol: '₦',
      decimalPlaces: 2,
      isActive: true,
      isBaseCurrency: false,
      exchangeRate: 1,
      country: 'NGA',
      metadata: {
        region: 'West Africa',
        centralBank: 'Central Bank of Nigeria',
        supportedPaymentMethods: ['bank_transfer', 'card', 'wallet'],
      },
    },
    {
      code: 'GHS',
      name: 'Ghanaian <PERSON>',
      symbol: 'GH₵',
      decimalPlaces: 2,
      isActive: true,
      isBaseCurrency: false,
      exchangeRate: 1,
      country: 'GHA',
      metadata: {
        region: 'West Africa',
        centralBank: 'Bank of Ghana',
        supportedPaymentMethods: ['mtn_mobile_money', 'airtel_money', 'bank_transfer', 'card'],
      },
    },
    {
      code: 'UGX',
      name: 'Ugandan Shilling',
      symbol: 'USh',
      decimalPlaces: 0,
      isActive: true,
      isBaseCurrency: false,
      exchangeRate: 1,
      country: 'UGA',
      metadata: {
        region: 'East Africa',
        centralBank: 'Bank of Uganda',
        supportedPaymentMethods: ['mtn_mobile_money', 'airtel_money', 'bank_transfer'],
      },
    },
    {
      code: 'TZS',
      name: 'Tanzanian Shilling',
      symbol: 'TSh',
      decimalPlaces: 2,
      isActive: true,
      isBaseCurrency: false,
      exchangeRate: 1,
      country: 'TZA',
      metadata: {
        region: 'East Africa',
        centralBank: 'Bank of Tanzania',
        supportedPaymentMethods: ['mpesa', 'airtel_money', 'bank_transfer'],
      },
    },
    {
      code: 'ZAR',
      name: 'South African Rand',
      symbol: 'R',
      decimalPlaces: 2,
      isActive: true,
      isBaseCurrency: false,
      exchangeRate: 1,
      country: 'ZAF',
      metadata: {
        region: 'Southern Africa',
        centralBank: 'South African Reserve Bank',
        supportedPaymentMethods: ['bank_transfer', 'card', 'wallet'],
      },
    },
    {
      code: 'RWF',
      name: 'Rwandan Franc',
      symbol: 'RF',
      decimalPlaces: 0,
      isActive: true,
      isBaseCurrency: false,
      exchangeRate: 1,
      country: 'RWA',
      metadata: {
        region: 'East Africa',
        centralBank: 'National Bank of Rwanda',
        supportedPaymentMethods: ['mtn_mobile_money', 'airtel_money', 'bank_transfer'],
      },
    },
    {
      code: 'ETB',
      name: 'Ethiopian Birr',
      symbol: 'Br',
      decimalPlaces: 2,
      isActive: true,
      isBaseCurrency: false,
      exchangeRate: 1,
      country: 'ETH',
      metadata: {
        region: 'East Africa',
        centralBank: 'National Bank of Ethiopia',
        supportedPaymentMethods: ['bank_transfer', 'card'],
      },
    },
    {
      code: 'USD',
      name: 'US Dollar',
      symbol: '$',
      decimalPlaces: 2,
      isActive: true,
      isBaseCurrency: true,
      exchangeRate: 1,
      country: 'USA',
      metadata: {
        region: 'Global',
        centralBank: 'Federal Reserve',
        supportedPaymentMethods: ['bank_transfer', 'card', 'wallet'],
      },
    },
  ];

  for (const currencyData of currencies) {
    const existingCurrency = await currencyRepository.findOne({
      where: { code: currencyData.code },
    });

    if (!existingCurrency) {
      const currency = currencyRepository.create(currencyData);
      await currencyRepository.save(currency);
      console.log(`Currency ${currencyData.code} seeded successfully`);
    } else {
      console.log(`Currency ${currencyData.code} already exists`);
    }
  }
}
