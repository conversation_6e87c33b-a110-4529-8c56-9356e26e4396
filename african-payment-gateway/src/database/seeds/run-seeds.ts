import { AppDataSource } from '../data-source';
import { seedCurrencies } from './currency.seed';

async function runSeeds() {
  try {
    console.log('Initializing database connection...');
    await AppDataSource.initialize();
    console.log('Database connection established');

    console.log('Starting database seeding...');

    // Seed currencies
    console.log('Seeding currencies...');
    await seedCurrencies(AppDataSource);

    console.log('Database seeding completed successfully!');
  } catch (error) {
    console.error('Error during database seeding:', error);
    process.exit(1);
  } finally {
    await AppDataSource.destroy();
    console.log('Database connection closed');
  }
}

runSeeds();
