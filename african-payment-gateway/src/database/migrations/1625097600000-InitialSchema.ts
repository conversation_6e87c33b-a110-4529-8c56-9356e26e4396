import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialSchema1625097600000 implements MigrationInterface {
  name = 'InitialSchema1625097600000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum types
    await queryRunner.query(`
      CREATE TYPE "public"."user_role_enum" AS ENUM(
        'super_admin', 
        'admin', 
        'merchant', 
        'support'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."user_status_enum" AS ENUM(
        'active', 
        'inactive', 
        'suspended', 
        'pending_verification'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."merchant_status_enum" AS ENUM(
        'pending', 
        'active', 
        'suspended', 
        'rejected'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."kyc_status_enum" AS ENUM(
        'not_started', 
        'in_progress', 
        'completed', 
        'rejected'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."business_type_enum" AS ENUM(
        'sole_proprietorship', 
        'partnership', 
        'limited_company', 
        'ngo', 
        'government', 
        'other'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."transaction_type_enum" AS ENUM(
        'payment', 
        'refund', 
        'payout', 
        'transfer'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."transaction_status_enum" AS ENUM(
        'pending', 
        'processing', 
        'completed', 
        'failed', 
        'cancelled', 
        'expired'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."payment_method_enum" AS ENUM(
        'mpesa', 
        'airtel_money', 
        'mtn_mobile_money', 
        'orange_money', 
        'bank_transfer', 
        'card', 
        'wallet'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."api_key_type_enum" AS ENUM(
        'public', 
        'secret', 
        'webhook'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."api_key_status_enum" AS ENUM(
        'active', 
        'inactive', 
        'revoked'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."webhook_status_enum" AS ENUM(
        'pending', 
        'delivered', 
        'failed', 
        'retrying', 
        'disabled'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."webhook_event_enum" AS ENUM(
        'payment.completed', 
        'payment.failed', 
        'payment.cancelled', 
        'refund.completed', 
        'refund.failed', 
        'payout.completed', 
        'payout.failed', 
        'merchant.verified', 
        'merchant.suspended'
      )
    `);

    await queryRunner.query(`
      CREATE TYPE "public"."audit_action_enum" AS ENUM(
        'create', 
        'update', 
        'delete', 
        'login', 
        'logout', 
        'payment_initiated', 
        'payment_completed', 
        'payment_failed', 
        'refund_initiated', 
        'refund_completed', 
        'merchant_verified', 
        'merchant_suspended', 
        'api_key_created', 
        'api_key_revoked', 
        'webhook_configured', 
        'settings_updated'
      )
    `);

    // Create users table
    await queryRunner.query(`
      CREATE TABLE "users" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "created_by" character varying(255),
        "updated_by" character varying(255),
        "email" character varying(255) NOT NULL,
        "password" character varying(255) NOT NULL,
        "first_name" character varying(100) NOT NULL,
        "last_name" character varying(100) NOT NULL,
        "phone_number" character varying(20),
        "role" "public"."user_role_enum" NOT NULL DEFAULT 'merchant',
        "status" "public"."user_status_enum" NOT NULL DEFAULT 'pending_verification',
        "email_verified_at" TIMESTAMP WITH TIME ZONE,
        "phone_verified_at" TIMESTAMP WITH TIME ZONE,
        "last_login_at" TIMESTAMP WITH TIME ZONE,
        "profile_picture" character varying(255),
        "preferred_language" character varying(10),
        "timezone" character varying(50),
        "two_factor_enabled" boolean NOT NULL DEFAULT false,
        "two_factor_secret" character varying(255),
        "metadata" jsonb,
        CONSTRAINT "UQ_users_email" UNIQUE ("email"),
        CONSTRAINT "UQ_users_phone_number" UNIQUE ("phone_number"),
        CONSTRAINT "PK_users" PRIMARY KEY ("id")
      )
    `);

    // Create merchants table
    await queryRunner.query(`
      CREATE TABLE "merchants" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "created_by" character varying(255),
        "updated_by" character varying(255),
        "business_name" character varying(255) NOT NULL,
        "business_registration_number" character varying(255) NOT NULL,
        "tax_identification_number" character varying(255),
        "business_type" "public"."business_type_enum" NOT NULL DEFAULT 'sole_proprietorship',
        "business_description" text,
        "business_address" character varying(255) NOT NULL,
        "business_city" character varying(100) NOT NULL,
        "business_state" character varying(100) NOT NULL,
        "business_country" character varying(3) NOT NULL,
        "business_postal_code" character varying(20),
        "contact_email" character varying(255) NOT NULL,
        "contact_phone" character varying(20) NOT NULL,
        "website" character varying(255),
        "status" "public"."merchant_status_enum" NOT NULL DEFAULT 'pending',
        "kyc_status" "public"."kyc_status_enum" NOT NULL DEFAULT 'not_started',
        "kyc_completed_at" TIMESTAMP WITH TIME ZONE,
        "transaction_fee_rate" decimal(5,4) NOT NULL DEFAULT 0.025,
        "minimum_transaction_fee" decimal(10,2) NOT NULL DEFAULT 0,
        "daily_transaction_limit" decimal(10,2) NOT NULL DEFAULT 1000000,
        "monthly_transaction_limit" decimal(10,2) NOT NULL DEFAULT 5000000,
        "allowed_payment_methods" jsonb,
        "allowed_currencies" jsonb,
        "logo_url" character varying(255),
        "bank_details" jsonb,
        "documents" jsonb,
        "settings" jsonb,
        "metadata" jsonb,
        "owner_id" uuid NOT NULL,
        CONSTRAINT "UQ_merchants_business_registration_number" UNIQUE ("business_registration_number"),
        CONSTRAINT "UQ_merchants_tax_identification_number" UNIQUE ("tax_identification_number"),
        CONSTRAINT "PK_merchants" PRIMARY KEY ("id")
      )
    `);

    // Create currencies table
    await queryRunner.query(`
      CREATE TABLE "currencies" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "created_by" character varying(255),
        "updated_by" character varying(255),
        "code" character varying(3) NOT NULL,
        "name" character varying(100) NOT NULL,
        "symbol" character varying(10) NOT NULL,
        "decimal_places" integer NOT NULL DEFAULT 2,
        "is_active" boolean NOT NULL DEFAULT true,
        "is_base_currency" boolean NOT NULL DEFAULT false,
        "exchange_rate" decimal(10,6) NOT NULL DEFAULT 1,
        "last_updated" TIMESTAMP WITH TIME ZONE,
        "country" character varying(3) NOT NULL,
        "metadata" jsonb,
        CONSTRAINT "UQ_currencies_code" UNIQUE ("code"),
        CONSTRAINT "PK_currencies" PRIMARY KEY ("id")
      )
    `);

    // Create transactions table
    await queryRunner.query(`
      CREATE TABLE "transactions" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "created_by" character varying(255),
        "updated_by" character varying(255),
        "reference" character varying(100) NOT NULL,
        "external_transaction_id" character varying(255),
        "type" "public"."transaction_type_enum" NOT NULL DEFAULT 'payment',
        "status" "public"."transaction_status_enum" NOT NULL DEFAULT 'pending',
        "payment_method" "public"."payment_method_enum" NOT NULL,
        "amount" decimal(15,2) NOT NULL,
        "fee" decimal(15,2) NOT NULL DEFAULT 0,
        "net_amount" decimal(15,2) NOT NULL,
        "currency" character varying(3) NOT NULL,
        "exchange_rate" decimal(10,6) NOT NULL DEFAULT 1,
        "base_currency" character varying(3),
        "base_amount" decimal(15,2),
        "description" text,
        "customer_name" character varying(255),
        "customer_email" character varying(255),
        "customer_phone" character varying(20),
        "customer_details" jsonb,
        "payment_details" jsonb,
        "callback_url" character varying(500),
        "return_url" character varying(500),
        "cancel_url" character varying(500),
        "expires_at" TIMESTAMP WITH TIME ZONE,
        "processed_at" TIMESTAMP WITH TIME ZONE,
        "settled_at" TIMESTAMP WITH TIME ZONE,
        "failure_reason" character varying(255),
        "provider_reference" character varying(100),
        "provider_response" jsonb,
        "metadata" jsonb,
        "is_test" boolean NOT NULL DEFAULT false,
        "ip_address" character varying(45),
        "user_agent" text,
        "merchant_id" uuid NOT NULL,
        CONSTRAINT "UQ_transactions_reference" UNIQUE ("reference"),
        CONSTRAINT "UQ_transactions_external_transaction_id" UNIQUE ("external_transaction_id"),
        CONSTRAINT "PK_transactions" PRIMARY KEY ("id")
      )
    `);

    // Create api_keys table
    await queryRunner.query(`
      CREATE TABLE "api_keys" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "created_by" character varying(255),
        "updated_by" character varying(255),
        "name" character varying(100) NOT NULL,
        "key_hash" character varying(255) NOT NULL,
        "key_prefix" character varying(50) NOT NULL,
        "type" "public"."api_key_type_enum" NOT NULL DEFAULT 'secret',
        "status" "public"."api_key_status_enum" NOT NULL DEFAULT 'active',
        "permissions" jsonb,
        "ip_whitelist" jsonb,
        "expires_at" TIMESTAMP WITH TIME ZONE,
        "last_used_at" TIMESTAMP WITH TIME ZONE,
        "usage_count" integer NOT NULL DEFAULT 0,
        "rate_limit" integer,
        "metadata" jsonb,
        "merchant_id" uuid NOT NULL,
        CONSTRAINT "UQ_api_keys_key_hash" UNIQUE ("key_hash"),
        CONSTRAINT "PK_api_keys" PRIMARY KEY ("id")
      )
    `);

    // Create webhooks table
    await queryRunner.query(`
      CREATE TABLE "webhooks" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "created_by" character varying(255),
        "updated_by" character varying(255),
        "url" character varying(500) NOT NULL,
        "event" "public"."webhook_event_enum" NOT NULL,
        "status" "public"."webhook_status_enum" NOT NULL DEFAULT 'pending',
        "payload" jsonb NOT NULL,
        "signature" character varying(255),
        "attempt_count" integer NOT NULL DEFAULT 0,
        "max_attempts" integer NOT NULL DEFAULT 3,
        "next_retry_at" TIMESTAMP WITH TIME ZONE,
        "delivered_at" TIMESTAMP WITH TIME ZONE,
        "response_status" integer,
        "response_body" text,
        "error_message" text,
        "headers" jsonb,
        "timeout_ms" integer NOT NULL DEFAULT 30000,
        "transaction_reference" character varying(100),
        "metadata" jsonb,
        "merchant_id" uuid NOT NULL,
        CONSTRAINT "PK_webhooks" PRIMARY KEY ("id")
      )
    `);

    // Create audit_logs table
    await queryRunner.query(`
      CREATE TABLE "audit_logs" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "created_by" character varying(255),
        "updated_by" character varying(255),
        "action" "public"."audit_action_enum" NOT NULL,
        "entity_type" character varying(100) NOT NULL,
        "entity_id" character varying(255),
        "old_values" jsonb,
        "new_values" jsonb,
        "ip_address" character varying(45),
        "user_agent" text,
        "description" text,
        "metadata" jsonb,
        "user_id" uuid,
        CONSTRAINT "PK_audit_logs" PRIMARY KEY ("id")
      )
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "merchants" 
      ADD CONSTRAINT "FK_merchants_users" 
      FOREIGN KEY ("owner_id") 
      REFERENCES "users"("id") 
      ON DELETE NO ACTION 
      ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "transactions" 
      ADD CONSTRAINT "FK_transactions_merchants" 
      FOREIGN KEY ("merchant_id") 
      REFERENCES "merchants"("id") 
      ON DELETE NO ACTION 
      ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "api_keys" 
      ADD CONSTRAINT "FK_api_keys_merchants" 
      FOREIGN KEY ("merchant_id") 
      REFERENCES "merchants"("id") 
      ON DELETE CASCADE 
      ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "webhooks" 
      ADD CONSTRAINT "FK_webhooks_merchants" 
      FOREIGN KEY ("merchant_id") 
      REFERENCES "merchants"("id") 
      ON DELETE CASCADE 
      ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "audit_logs" 
      ADD CONSTRAINT "FK_audit_logs_users" 
      FOREIGN KEY ("user_id") 
      REFERENCES "users"("id") 
      ON DELETE SET NULL 
      ON UPDATE NO ACTION
    `);

    // Create indexes
    await queryRunner.query(`
      CREATE INDEX "IDX_users_email" ON "users" ("email")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_users_phone_number" ON "users" ("phone_number")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_users_role" ON "users" ("role")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_users_status" ON "users" ("status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_merchants_business_registration_number" ON "merchants" ("business_registration_number")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_merchants_tax_identification_number" ON "merchants" ("tax_identification_number")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_merchants_status" ON "merchants" ("status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_merchants_kyc_status" ON "merchants" ("kyc_status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_merchants_owner_id" ON "merchants" ("owner_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_currencies_code" ON "currencies" ("code")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_currencies_is_active" ON "currencies" ("is_active")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_transactions_reference" ON "transactions" ("reference")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_transactions_external_transaction_id" ON "transactions" ("external_transaction_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_transactions_status" ON "transactions" ("status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_transactions_payment_method" ON "transactions" ("payment_method")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_transactions_merchant_id" ON "transactions" ("merchant_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_transactions_created_at" ON "transactions" ("created_at")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_api_keys_merchant_id" ON "api_keys" ("merchant_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_api_keys_key_prefix" ON "api_keys" ("key_prefix")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_api_keys_status" ON "api_keys" ("status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_webhooks_merchant_id_event" ON "webhooks" ("merchant_id", "event")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_webhooks_status_next_retry_at" ON "webhooks" ("status", "next_retry_at")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_audit_logs_user_id_action" ON "audit_logs" ("user_id", "action")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_audit_logs_entity_type_entity_id" ON "audit_logs" ("entity_type", "entity_id")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_audit_logs_created_at" ON "audit_logs" ("created_at")
    `);

    // Create extension for UUID generation if it doesn't exist
    await queryRunner.query(`
      CREATE EXTENSION IF NOT EXISTS "uuid-ossp"
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign keys
    await queryRunner.query(`ALTER TABLE "audit_logs" DROP CONSTRAINT "FK_audit_logs_users"`);
    await queryRunner.query(`ALTER TABLE "webhooks" DROP CONSTRAINT "FK_webhooks_merchants"`);
    await queryRunner.query(`ALTER TABLE "api_keys" DROP CONSTRAINT "FK_api_keys_merchants"`);
    await queryRunner.query(`ALTER TABLE "transactions" DROP CONSTRAINT "FK_transactions_merchants"`);
    await queryRunner.query(`ALTER TABLE "merchants" DROP CONSTRAINT "FK_merchants_users"`);

    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_audit_logs_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_audit_logs_entity_type_entity_id"`);
    await queryRunner.query(`DROP INDEX "IDX_audit_logs_user_id_action"`);
    await queryRunner.query(`DROP INDEX "IDX_webhooks_status_next_retry_at"`);
    await queryRunner.query(`DROP INDEX "IDX_webhooks_merchant_id_event"`);
    await queryRunner.query(`DROP INDEX "IDX_api_keys_status"`);
    await queryRunner.query(`DROP INDEX "IDX_api_keys_key_prefix"`);
    await queryRunner.query(`DROP INDEX "IDX_api_keys_merchant_id"`);
    await queryRunner.query(`DROP INDEX "IDX_transactions_created_at"`);
    await queryRunner.query(`DROP INDEX "IDX_transactions_merchant_id"`);
    await queryRunner.query(`DROP INDEX "IDX_transactions_payment_method"`);
    await queryRunner.query(`DROP INDEX "IDX_transactions_status"`);
    await queryRunner.query(`DROP INDEX "IDX_transactions_external_transaction_id"`);
    await queryRunner.query(`DROP INDEX "IDX_transactions_reference"`);
    await queryRunner.query(`DROP INDEX "IDX_currencies_is_active"`);
    await queryRunner.query(`DROP INDEX "IDX_currencies_code"`);
    await queryRunner.query(`DROP INDEX "IDX_merchants_owner_id"`);
    await queryRunner.query(`DROP INDEX "IDX_merchants_kyc_status"`);
    await queryRunner.query(`DROP INDEX "IDX_merchants_status"`);
    await queryRunner.query(`DROP INDEX "IDX_merchants_tax_identification_number"`);
    await queryRunner.query(`DROP INDEX "IDX_merchants_business_registration_number"`);
    await queryRunner.query(`DROP INDEX "IDX_users_status"`);
    await queryRunner.query(`DROP INDEX "IDX_users_role"`);
    await queryRunner.query(`DROP INDEX "IDX_users_phone_number"`);
    await queryRunner.query(`DROP INDEX "IDX_users_email"`);

    // Drop tables
    await queryRunner.query(`DROP TABLE "audit_logs"`);
    await queryRunner.query(`DROP TABLE "webhooks"`);
    await queryRunner.query(`DROP TABLE "api_keys"`);
    await queryRunner.query(`DROP TABLE "transactions"`);
    await queryRunner.query(`DROP TABLE "currencies"`);
    await queryRunner.query(`DROP TABLE "merchants"`);
    await queryRunner.query(`DROP TABLE "users"`);

    // Drop enum types
    await queryRunner.query(`DROP TYPE "public"."audit_action_enum"`);
    await queryRunner.query(`DROP TYPE "public"."webhook_event_enum"`);
    await queryRunner.query(`DROP TYPE "public"."webhook_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."api_key_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."api_key_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."payment_method_enum"`);
    await queryRunner.query(`DROP TYPE "public"."transaction_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."transaction_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."business_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."kyc_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."merchant_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."user_status_enum"`);
    await queryRunner.query(`DROP TYPE "public"."user_role_enum"`);
  }
}
