import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AppService {
  constructor(private readonly configService: ConfigService) {}

  getHello(): string {
    return 'Welcome to the African Payment Gateway API';
  }

  getHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: this.configService.get('NODE_ENV'),
    };
  }

  getVersion() {
    return {
      name: this.configService.get('APP_NAME', 'African Payment Gateway'),
      version: this.configService.get('APP_VERSION', '1.0.0'),
      apiVersion: 'v1',
    };
  }
}
